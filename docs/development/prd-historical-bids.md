# PRD: Enhanced RFQ Estimations & Provider Matching with Historical Data

**Version:** 2.0
**Date:** 2024-12-19
**Author:** Steelflow Development Team
**Status:** Ready for Implementation

## 1. Introduction

### 1.1. Problem
Currently, Steelflow's RFQ process operates on available provider data since launch. While effective for operational booking, it lacks historical context for:
*   Providing quick, approximate price estimations for sales/deal negotiations *before* a formal RFQ is sent.
*   Leveraging past successful bookings to identify and rank experienced providers for new RFQs.
We possess valuable historical data (approx. 300 booked routes) in a CSV format, which is currently not integrated into Steelflow.

### 1.2. Goal
To integrate historical booked route data into Steelflow in a non-disruptive way to:
1.  Enable a new "Logistics Estimator" page for generating approximate price per kilometer/total price based on historical data and current RFQ bids.
2.  Enhance the existing provider matching algorithm by flagging or ranking providers with a successful history on similar lanes.
3.  Maintain the data integrity of the current operational Steelflow database.

### 1.3. Scope
*   **In Scope:**
    *   Defining a database schema for storing historical booked routes.
    *   Guidance for a one-time CSV import of historical data via Supabase dashboard or pgAdmin.
    *   Developing a new "Logistics Estimator" page/feature using Next.js 15 App Router.
    *   Modifying the existing provider matching service to consider historical performance.
    *   Support for approximate route matching (e.g., based on the first 2 digits of French postal codes).
    *   Integration with existing service-layer architecture and TanStack Query patterns.
*   **Out of Scope:**
    *   Automated ETL pipeline for continuous data synchronization as it is a one time activity.
    *   Complex AI/ML models for price prediction beyond statistical analysis of historical/current data.
    *   Direct modification or "injection" of historical data into current operational RFQ/bid tables.

## 2. Target Users
*   **Logistics Managers:** Primary users who will use the estimator for pre-RFQ quoting and benefit from enhanced provider matching.
*   **Sales/Account Managers:** Users who need quick, indicative pricing for deals.
*   **Administrators:** Users who might oversee the data import and system integrity.

## 3. User Stories

*   **US1 (Estimator):** As a Logistics Manager, I want to input an origin (country, city, postal code or a postal code prefix, i.e FR95 for Paris region, DE12 for Berlin region), destination (country, city, postal code or postal code prefix such as ES05 for Barcelona region), cargo type(s), equipment type, and weight, so that I can get an estimated price range (min, max, average, median) and price per kilometer based on historical bookings and recent similar RFQ bids.
*   **US2 (Estimator - Approximation):** As a Logistics Manager, when estimating for a route, I want the system to consider historical routes matching the first two digits of the postal code (e.g., FR95xxx) if an exact match isn't available, so I can get a relevant estimate even for new specific locations within a known region.
*   **US3 (Provider Matching):** As a Logistics Manager, when creating an RFQ, I want the system to highlight or rank providers who have successfully completed similar historical routes, so I can prioritize reliable partners.
*   **US4 (Data Integrity):** As an Administrator, I want to import historical data without affecting the quality or structure of my current operational RFQ and provider data.

## 4. Proposed Solution

### 4.1. High-Level Approach
1.  **Historical Data Storage:** Create dedicated tables within the Steelflow Supabase database to store the imported historical booked routes and their associated cargo types. These tables will be separate from the operational RFQ/bid tables.
2.  **One-Time Data Import:** The user will perform a one-time import of the cleaned data (exported as CSV) into these new historical tables using Supabase dashboard or pgAdmin.
3.  **Estimations Feature:**
    *   A new UI page/section ("Logistics Estimator") at `/dashboard/estimator`.
    *   A new service (`src/lib/services/estimation.service.ts`) will query both the `historical_bids` and the current `rfq_bids` (for recent, relevant bids) to calculate price estimates.
    *   The service will support approximate matching for postal code prefixes (e.g., first 2 digits for France).
    *   Integration with TanStack Query for data fetching and caching.
4.  **Enhanced Provider Matching:**
    *   The existing `src/lib/services/rfq.service.ts` (`matchProvidersForRFQ` function) will be augmented to query the `historical_bids` table.
    *   Providers identified from historical data for similar routes will be flagged or given a higher relevance score in the provider selection UI.

### 4.2. Data Flow for Estimations (Service-Layer Architecture)
```
User Inputs (Origin, Dest, Cargo, etc.) on Estimator Page
                    ↓
        Estimator UI Component (React)
                    ↓
    getCombinedPriceEstimatesAction (Server Action)
                    ↓
estimation.service.ts → getCombinedPriceEstimates
                    ↓
        ┌─────────────────────┬─────────────────────┐
        ↓                     ↓                     ↓
Query historical_bids    Query rfq_bids      Apply Filters
                    ↓                     ↓
        ┌─────────────────────┬─────────────────────┐
                    ↓
Aggregate, Calculate Stats (min, max, avg, median, price/km)
                    ↓
        Return EstimationResult to UI
```
## 5. Detailed Features

### 5.1. Historical Data Integration
*   **5.1.1. Database Schema for Historical Data:**
    *   Table: `historical_bids` (aligned with current Supabase schema conventions)
        ```sql
        CREATE TABLE public.historical_bids (
            id uuid PRIMARY KEY DEFAULT uuid_generate_v4() NOT NULL,
            origin_country_id uuid REFERENCES public.countries(id) ON DELETE SET NULL, -- Nullable if mapping fails
            origin_country_raw text, -- Store raw country name from CSV
            origin_city_raw text,
            origin_postal_code_raw text,
            origin_postal_prefix char(2), -- First 2 digits of postal code (applicable to European countries)
            destination_country_id uuid REFERENCES public.countries(id) ON DELETE SET NULL, -- Nullable
            destination_country_raw text,
            destination_city_raw text,
            destination_postal_code_raw text,
            destination_postal_prefix char(2), -- First 2 digits of postal code (applicable to European countries)
            mapped_equipment_type_id uuid REFERENCES public.equipment_types(id) ON DELETE SET NULL, -- Nullable
            equipment_description_raw text,
            weight_tonnes numeric,
            booked_price numeric NOT NULL CONSTRAINT historical_bids_price_check CHECK ((booked_price >= (0)::numeric)),
            price_currency text DEFAULT 'EUR'::text NOT NULL,
            provider_name_raw text,
            mapped_provider_id uuid REFERENCES public.providers(id) ON DELETE SET NULL, -- Nullable
            booking_date date,
            price_per_km numeric, -- Optional: Can be pre-calculated or calculated on the fly
            distance_km numeric, -- Optional: If available in data
            source_reference text, -- e.g., "CSV Row X"
            notes_raw text,
            created_at timestamp with time zone DEFAULT now() NOT NULL,
            updated_at timestamp with time zone DEFAULT now() NOT NULL
        );

        -- Comments following Steelflow conventions
        COMMENT ON TABLE public.historical_bids IS 'Stores historical bid data for price estimation and provider matching';
        COMMENT ON COLUMN public.historical_bids.id IS 'Primary key: Unique identifier for the historical bid (UUID).';
        COMMENT ON COLUMN public.historical_bids.booked_price IS 'The final booked price for this historical route.';
        COMMENT ON COLUMN public.historical_bids.price_currency IS 'The currency of the booked price (default: EUR).';
        COMMENT ON COLUMN public.historical_bids.booking_date IS 'Date when this route was originally booked.';

        -- Indexes for faster querying
        CREATE INDEX idx_historical_bids_origin_country ON public.historical_bids USING btree (origin_country_id);
        CREATE INDEX idx_historical_bids_destination_country ON public.historical_bids USING btree (destination_country_id);
        CREATE INDEX idx_historical_bids_origin_postal_prefix ON public.historical_bids USING btree (origin_postal_prefix);
        CREATE INDEX idx_historical_bids_destination_postal_prefix ON public.historical_bids USING btree (destination_postal_prefix);
        CREATE INDEX idx_historical_bids_equipment_type ON public.historical_bids USING btree (mapped_equipment_type_id);
        CREATE INDEX idx_historical_bids_provider ON public.historical_bids USING btree (mapped_provider_id);
        CREATE INDEX idx_historical_bids_booking_date ON public.historical_bids USING btree (booking_date);
        ```
    *   Table: `historical_bid_cargo_types` (Junction Table - aligned with current naming conventions)
        ```sql
        CREATE TABLE public.historical_bid_cargo_types (
            id uuid PRIMARY KEY DEFAULT uuid_generate_v4() NOT NULL,
            historical_bid_id uuid NOT NULL REFERENCES public.historical_bids(id) ON DELETE CASCADE,
            mapped_cargo_type_id uuid REFERENCES public.cargo_types(id) ON DELETE SET NULL, -- Nullable
            cargo_description_item_raw text NOT NULL, -- Store raw item description
            quantity integer,
            created_at timestamp with time zone DEFAULT now() NOT NULL
        );

        -- Comments following Steelflow conventions
        COMMENT ON TABLE public.historical_bid_cargo_types IS 'Junction table linking historical bids with cargo types';
        COMMENT ON COLUMN public.historical_bid_cargo_types.historical_bid_id IS 'Foreign key to historical_bids table';
        COMMENT ON COLUMN public.historical_bid_cargo_types.mapped_cargo_type_id IS 'Foreign key to cargo_types table (nullable if mapping fails)';

        CREATE INDEX idx_historical_bid_cargo_types_bid_id ON public.historical_bid_cargo_types USING btree (historical_bid_id);
        CREATE INDEX idx_historical_bid_cargo_types_cargo_type_id ON public.historical_bid_cargo_types USING btree (mapped_cargo_type_id);

        -- RLS following Steelflow patterns
        ALTER TABLE public.historical_bids ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.historical_bid_cargo_types ENABLE ROW LEVEL SECURITY;

        -- Policies: Allow authenticated users to access historical data
        CREATE POLICY "Authenticated users can access historical bids"
        ON public.historical_bids FOR ALL USING (auth.role() = 'authenticated');

        CREATE POLICY "Authenticated users can access historical bid cargo types"
        ON public.historical_bid_cargo_types FOR ALL USING (auth.role() = 'authenticated');

        -- Triggers for updated_at
        CREATE TRIGGER update_historical_bids_updated_at BEFORE UPDATE ON public.historical_bids FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
        ```
*   **5.1.2. CSV Import Guidance:**
    *   Provide a template/example CSV structure matching the historical tables.
    *   User will need to pre-process their data to fit this CSV structure.
    *   Key pre-processing:
        *   Split multiple cargo items for a single route into separate rows in a "cargo items" CSV or handle this during import.
        *   Extract French postal code prefixes (`FRXX`) into a separate column if possible, or this can be done via SQL `UPDATE` statement post-import.
    *   User imports data using Supabase dashboard CSV import tool or pgAdmin. `mapped_..._id` fields will initially be NULL or contain raw text if direct FK mapping isn't done in the CSV.
*   **5.1.3. Post-Import Mapping (Optional but Recommended - Server-Side Script):**
    *   A one-time script could attempt to populate `mapped_country_id`, `mapped_provider_id`, `mapped_equipment_type_id`, `mapped_cargo_type_id` by matching raw text fields against current Steelflow master data.
    *   **Postal Prefix Extraction Logic:**
        *   Extract first 2 digits from postal codes for European countries that use numeric postal codes
        *   Examples:
            *   France: `UPDATE historical_bids SET origin_postal_prefix = SUBSTRING(origin_postal_code_raw FROM 1 FOR 2) WHERE origin_country_raw = 'France' AND origin_postal_code_raw ~ '^[0-9]{5}$';`
            *   Germany: `UPDATE historical_bids SET origin_postal_prefix = SUBSTRING(origin_postal_code_raw FROM 1 FOR 2) WHERE origin_country_raw = 'Germany' AND origin_postal_code_raw ~ '^[0-9]{5}$';`
            *   Spain: `UPDATE historical_bids SET origin_postal_prefix = SUBSTRING(origin_postal_code_raw FROM 1 FOR 2) WHERE origin_country_raw = 'Spain' AND origin_postal_code_raw ~ '^[0-9]{5}$';`
        *   **Important**: This logic should be implemented in the service layer for real-time estimation requests, not just for historical data

### 5.2. Enhanced Estimations Page (Next.js 15 App Router)
*   **5.2.1. UI Implementation:**
    *   **Location:** `src/app/dashboard/estimator/page.tsx`
    *   **Components:** `src/app/dashboard/estimator/_components/`
        *   `estimator-form.tsx` - Form component using React Hook Form + Zod validation
        *   `estimation-results.tsx` - Results display component
        *   `estimation-data-table.tsx` - Optional: Table showing individual data points used
    *   **UI Inputs (using shadcn/ui components):**
        *   Origin: Country (Select), City (Input), Postal Code / Prefix (Input)
        *   Destination: Country (Select), City (Input), Postal Code / Prefix (Input)
        *   Cargo Type(s): Multi-select from `cargo_types` table
        *   Equipment Type: Select from `equipment_types` table
        *   Weight (tonnes): Number input with validation
*   **5.2.2. Service Layer (`src/lib/services/estimation.service.ts`):**
    *   Function `getCombinedPriceEstimates(params)`:
        *   Accepts validated UI inputs via Zod schema
        *   Queries `historical_bids` (joining `historical_bid_cargo_types`):
            *   Filter by exact country matches using `origin_country_id` and `destination_country_id`
            *   Filter by `mapped_equipment_type_id`
            *   Filter by `mapped_cargo_type_id` (matching any of the selected cargo types)
            *   Filter by weight range (e.g., `params.weight_tonnes * 0.8` to `params.weight_tonnes * 1.2`)
            *   **Postal Code Matching (Service-Layer Logic):**
                *   **Dynamic Prefix Extraction**: For European countries with numeric postal codes, extract first 2 digits in service layer
                *   **Matching Strategy**:
                    *   If user inputs 2 digits (e.g., "95"), treat as prefix and match against `origin_postal_prefix`
                    *   If user inputs full postal code (e.g., "95100"), extract first 2 digits and match against `origin_postal_prefix`
                    *   Also attempt exact match on `origin_postal_code_raw` for precise matches
                *   **Country-Agnostic Approach**: Apply prefix logic to all European countries with numeric postal codes (France, Germany, Spain, Italy, etc.)
        *   Queries `rfq_bids` (joining `rfqs`):
            *   Apply similar filters using existing RFQ schema
            *   Consider only bids with status 'accepted' or recent 'received'/'under_review' (last 3-6 months)
        *   Combine price points from both sources
        *   Calculate: Min, Max, Average, Median price
        *   Calculate: Min, Max, Average, Median Price/km (using existing distance calculation service if available)
        *   Return statistics and `dataPointsCount`
*   **5.2.3. Action Layer (`src/lib/actions/estimation.actions.ts`):**
    *   `getCombinedPriceEstimatesAction` - Server Action calling the service
    *   Input validation using Zod schemas from `src/lib/schemas/estimation.schema.ts`
    *   Error handling and ActionResponse pattern
*   **5.2.4. TanStack Query Integration:**
    *   Custom hook: `src/lib/hooks/use-price-estimation.ts`
    *   Query key: `['price-estimation', params]`
    *   Caching and background refetching
*   **5.2.5. UI Outputs:**
    *   Display calculated price statistics in cards/metrics layout
    *   Display number of data points used for confidence indication
    *   Breakdown: "X historical routes, Y recent bids"
    *   Optional: Export functionality for estimates

### 5.3. Improved Provider Matching/Ranking (Service Layer Integration)
*   **5.3.1. Augment `src/lib/services/rfq.service.ts` -> `matchProvidersForRFQ`:**
    *   After existing matching logic (based on `provider_routes`, `provider_equipments`), perform an additional query
    *   Query `historical_bids`:
        ```sql
        SELECT mapped_provider_id, COUNT(*) as historical_bids_count
        FROM historical_bids hb
        JOIN historical_bid_cargo_types hbct ON hb.id = hbct.historical_bid_id
        WHERE
            hb.origin_country_id = $1
            AND hb.destination_country_id = $2
            AND hb.mapped_equipment_type_id = $3
            AND hbct.mapped_cargo_type_id = ANY($4)
            AND hb.mapped_provider_id IS NOT NULL
        GROUP BY mapped_provider_id;
        ```
    *   Merge this historical performance data with the list of operationally matched providers
    *   Return enhanced provider data with `historicalExperience` field
*   **5.3.2. UI Update in Provider Selection Components:**
    *   **Location:** `src/app/dashboard/rfqs/[id]/_components/provider-selection/`
    *   **Components to update:**
        *   Provider selection data table columns
        *   Provider cards/list items
    *   **UI Enhancements:**
        *   Display badge: "Experienced" or "✅ Historical Experience" for providers with `historical_bids_count > 0`
        *   Show `historical_bids_count` on hover or in detail column
        *   Add sorting option by historical experience
        *   Use existing badge/icon patterns from shadcn/ui

## 6. Data Model (Recap of New Tables)
*   `public.historical_bids` (as defined in 5.1.1)
*   `public.historical_bid_cargo_types` (as defined in 5.1.1)

## 7. Schema Integration
*   **7.1. Zod Schemas (`src/lib/schemas/estimation.schema.ts`):**
    ```typescript
    import { z } from "zod";
    import * as generated from "./schemas";

    // Estimation request schema
    export const EstimationRequestSchema = z.object({
      origin_country_id: z.string().uuid("Please provide a valid origin country ID"),
      origin_city: z.string().min(1, "Origin city is required"),
      origin_postal_code: z.string().optional(),
      destination_country_id: z.string().uuid("Please provide a valid destination country ID"),
      destination_city: z.string().min(1, "Destination city is required"),
      destination_postal_code: z.string().optional(),
      cargo_type_ids: z.array(z.string().uuid()).min(1, "At least one cargo type is required"),
      equipment_type_id: z.string().uuid("Please provide a valid equipment type ID"),
      weight_tonnes: z.number().positive("Weight must be positive"),
    });

    // Estimation result schema
    export const EstimationResultSchema = z.object({
      price_statistics: z.object({
        min: z.number(),
        max: z.number(),
        average: z.number(),
        median: z.number(),
      }),
      price_per_km_statistics: z.object({
        min: z.number(),
        max: z.number(),
        average: z.number(),
        median: z.number(),
      }).optional(),
      data_points_count: z.number(),
      historical_count: z.number(),
      recent_bids_count: z.number(),
    });

    // Historical bid schema (for Supazod generation)
    export const HistoricalBidSchema = generated.publicHistoricalBidsRowSchemaSchema;
    ```

## 8. Technical Considerations
*   **CSV Import:** The user is responsible for the quality of the CSV. The system will import what's given. Errors in the CSV (e.g., text in a numeric column) might cause Supabase import to fail or import bad data.
*   **Mapping Robustness:** Mapping raw text (e.g., `provider_name_raw`) to existing Steelflow IDs (`mapped_provider_id`) during query time can be complex and less performant than pre-mapped FKs. A post-import batch script to populate these `mapped_..._id` fields is highly recommended for better performance and data consistency.
*   **Performance:** Queries involving postal prefix matching or joining multiple tables for estimation will benefit from the defined indexes on `historical_bids` and `historical_bid_cargo_types`. Monitor query performance and consider additional indexes if needed.
*   **Scalability of Estimator:** If the number of historical routes and current bids grows very large, the estimation query might become slow. Consider optimizations like:
    *   Pre-aggregating some statistics
    *   Implementing query result caching via TanStack Query
    *   Using database views for complex joins
*   **UI for Postal Prefix:** The Estimator UI needs to clearly indicate when a user can/should enter a full postal code vs. a prefix (especially for French postal codes).
*   **Google Maps API Costs:** Distance calculations using the existing Google Maps service should be used judiciously to control API costs. Consider caching calculated distances.
*   **Data Validation:** Implement proper validation in the service layer to ensure data integrity when processing estimation requests.
*   **Error Handling:** Follow existing error handling patterns in the service layer and provide meaningful error messages to users.

## 9. Success Metrics
*   **Estimator Usage:** Number of times the Logistics Estimator page is used per week.
*   **Estimation Accuracy (Qualitative):** Feedback from Logistics/Sales Managers on how well the estimates align with actual bid prices over time.
*   **Provider Selection:** Increase in selection of providers flagged as "historically experienced" for relevant RFQs.
*   **Time Savings:** Reduction in time spent by managers manually researching historical prices or suitable providers.

## 10. Future Considerations
*   More sophisticated mapping tools/UI for linking raw historical text to Steelflow entities.
*   Enhanced integration with existing distance calculation service for more accurate price/km calculations.
*   Weighting recent data more heavily in estimations.
*   Machine learning models for more sophisticated price prediction.
*   Real-time data synchronization if historical data sources become dynamic.

## 11. Task Breakdown

> **🎯 RECOMMENDED IMPLEMENTATION STRATEGY**
>
> **Optimal Sequence**: Foundation → MVP Estimator → Enhanced Estimator → Provider Matching → Integration
>
> **Key Benefits**:
> - ✅ **Sequential Development**: One feature at a time for focused development
> - ✅ **Risk Mitigation**: Test each component thoroughly before proceeding
> - ✅ **User Feedback**: Early MVP delivery allows for validation and refinement
> - ✅ **Technical Stability**: Solid foundation before adding complexity

### Phase 1: Historical Data Foundation

*   [ ] **DB:** Create migration file: `supabase/migrations/YYYYMMDDHHMMSS_create_historical_bids_tables.sql`
*   [ ] **DB:** Define and create `historical_bids` table schema in Supabase (following current conventions)
*   [ ] **DB:** Define and create `historical_bid_cargo_types` table schema in Supabase
*   [ ] **DB:** Add necessary indexes, RLS policies, and triggers to historical tables
*   [ ] **SCHEMA:** Create `src/lib/schemas/historical-bid.schema.ts` with Zod schemas
*   [ ] **SCHEMA:** Update `src/lib/schemas/index.ts` to export historical bid types
*   [ ] **TEST:** Write basic schema validation tests
*   [ ] **DOC:** Prepare CSV template and import instructions for Supabase dashboard/pgAdmin
*   [ ] **DATA:** User performs one-time CSV import of ~300 historical routes
*   [ ] **DEV (Optional but Recommended):** Develop a one-time server-side script to:
    *   [ ] Populate `origin_postal_prefix_fr` and `destination_postal_prefix_fr` from `_raw` fields
    *   [ ] Attempt to map `_country_raw` to `_country_id`
    *   [ ] Attempt to map `provider_name_raw` to `mapped_provider_id`
    *   [ ] Attempt to map `equipment_description_raw` to `mapped_equipment_type_id`
    *   [ ] Attempt to map `cargo_description_item_raw` to `mapped_cargo_type_id` in the junction table

### Phase 2: MVP Logistics Estimator (Core Functionality)

**🎯 Goal**: Deliver functional price estimation for immediate business value

*   [ ] **SCHEMA:** Create `src/lib/schemas/estimation.schema.ts` with request/response schemas
*   [ ] **SERVICE:** Create `src/lib/services/estimation.service.ts`
    *   [ ] Implement `getCombinedPriceEstimates(params)` function (MVP version)
        *   [ ] Query `historical_bids` with basic filters (country, equipment, cargo, weight)
        *   [ ] Implement postal code prefix extraction logic for European countries
        *   [ ] Query `rfq_bids` (recent/relevant) with similar filters
        *   [ ] Combine price points and calculate basic statistics (min, max, avg)
        *   [ ] Return structured estimation results
*   [ ] **ACTION:** Create `src/lib/actions/estimation.actions.ts` with server actions
*   [ ] **HOOK:** Create `src/lib/hooks/use-price-estimation.ts` for TanStack Query integration
*   [ ] **UI:** Create MVP estimator page:
    *   [ ] `src/app/dashboard/estimator/page.tsx` - Main page with basic layout
    *   [ ] `src/app/dashboard/estimator/_components/estimator-form.tsx` - Simple form component
    *   [ ] `src/app/dashboard/estimator/_components/estimation-results.tsx` - Basic results display
*   [ ] **UI:** Implement form validation using React Hook Form + Zod
*   [ ] **UI:** Implement basic results display with loading and error states
*   [ ] **NAV:** Add estimator page to dashboard navigation
*   [ ] **TEST:** Write unit tests for core estimation service functions
*   [ ] **TEST:** Write integration tests for estimation actions

### Phase 3: Enhanced Estimator Features

**🎯 Goal**: Add advanced features and polish to the estimator

*   [ ] **SERVICE:** Enhance `getCombinedPriceEstimates` function:
    *   [ ] Add median calculation and price/km statistics
    *   [ ] Integrate with existing distance calculation service
    *   [ ] Add weight range filtering (±20% tolerance)
    *   [ ] Optimize postal code prefix matching performance
*   [ ] **UI:** Enhanced estimator features:
    *   [ ] `src/app/dashboard/estimator/_components/estimation-data-table.tsx` - Data table showing individual data points
    *   [ ] Advanced filtering options (date ranges, provider types)
    *   [ ] Export functionality for estimates
    *   [ ] Confidence indicators based on data points count
*   [ ] **UI:** Polish and UX improvements:
    *   [ ] Better loading states and skeleton UI
    *   [ ] Improved error handling and user feedback
    *   [ ] Tooltips and help text for postal code prefix usage
*   [ ] **TEST:** Write comprehensive tests for enhanced estimation features

### Phase 4: Enhanced Provider Matching

**🎯 Goal**: Improve provider selection with historical experience data

*   [ ] **SERVICE:** Modify `src/lib/services/rfq.service.ts` -> `matchProvidersForRFQ` function
    *   [ ] Add logic to query `historical_bids` for providers who served similar routes
    *   [ ] Implement historical experience scoring algorithm
    *   [ ] Return matched providers with `historicalExperience` field
    *   [ ] Ensure backward compatibility with existing provider matching
*   [ ] **UI:** Update Provider Selection components in `src/app/dashboard/rfqs/[id]/_components/`
    *   [ ] Add historical experience badges/indicators using existing shadcn/ui patterns
    *   [ ] Add sorting option by historical experience in data table
    *   [ ] Update data table columns to show historical data
    *   [ ] Add tooltips showing historical bid count on hover
*   [ ] **TEST:** Write unit tests for enhanced provider matching logic
*   [ ] **TEST:** Write integration tests for provider matching UI updates
*   [ ] **DOC:** Update `src/app/dashboard/rfqs/README.md` with provider matching changes

### Phase 5: Final Integration & Documentation

**🎯 Goal**: Ensure system stability and comprehensive documentation

*   [ ] **INTEGRATION:** End-to-end testing of complete historical data workflow
*   [ ] **PERFORMANCE:** Monitor and optimize query performance for historical data
*   [ ] **SECURITY:** Review RLS policies and data access patterns
*   [ ] **DOC:** Create comprehensive documentation:
    *   [ ] `src/app/dashboard/estimator/README.md` for the estimator feature
    *   [ ] User documentation for the new Estimator page
    *   [ ] Admin guide for historical data management
    *   [ ] API documentation for estimation endpoints
*   [ ] **TRAINING:** Prepare user training materials for Logistics and Sales teams
*   [ ] **MONITORING:** Set up analytics for success metrics tracking

---

## � **Critical Implementation Details**

### **Postal Code Prefix Handling Strategy**

**Problem**: How to handle postal code prefixes for estimation matching across different European countries?

**Solution**: Service-layer logic with country-agnostic approach

```typescript
// Example service function for postal prefix extraction
function extractPostalPrefix(postalCode: string, countryCode: string): string | null {
  // European countries with numeric postal codes
  const numericPostalCountries = ['FR', 'DE', 'ES', 'IT', 'BE', 'NL', 'AT'];

  if (!numericPostalCountries.includes(countryCode)) {
    return null; // No prefix logic for non-numeric postal systems
  }

  // Extract first 2 digits for numeric postal codes
  const match = postalCode.match(/^(\d{2})/);
  return match ? match[1] : null;
}
```

**Database Strategy**:
- Store `origin_postal_prefix` and `destination_postal_prefix` in database for performance
- Populate during data import and via service layer for real-time requests
- Index these fields for fast querying

**Estimation Matching Logic**:
1. User inputs postal code (full or prefix)
2. Service extracts prefix if full postal code provided
3. Query matches against stored `postal_prefix` fields
4. Fallback to exact postal code matching if prefix yields no results
