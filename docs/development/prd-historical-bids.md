# PRD: Enhanced RFQ Estimations & Provider Matching with Historical Data

**Version:** 1.0
**Date:** 2023-10-27
**Author:** Steelflow AI Assistant
**Status:** Proposed

## 1. Introduction

### 1.1. Problem
Currently, Steelflow's RFQ process operates on available provider data since launch, i.e 2 weeks ago. While effective for operational booking, it lacks historical context for:
*   Providing quick, approximate price estimations for sales/deal negotiations *before* a formal RFQ is sent.
*   Leveraging past successful bookings to identify and rank experienced providers for new RFQs.
We possess valuable historical data (approx. 300 booked routes) in a CSV format, which is currently not integrated into Steelflow.

### 1.2. Goal
To integrate historical booked route data into Steelflow in a non-disruptive way to:
1.  Enable a new "Logistics Estimator" page for generating approximate price per kilometer/total price based on historical data and current RFQ bids.
2.  Enhance the existing provider matching algorithm by flagging or ranking providers with a successful history on similar lanes.
3.  Maintain the data integrity of the current operational Steelflow database.

### 1.3. Scope
*   **In Scope:**
    *   Defining a database schema for storing historical booked routes.
    *   Guidance for a one-time CSV import of historical data via pgAdmin (or similar tool).
    *   Developing a new "Logistics Estimator" page/feature.
    *   Modifying the existing provider matching service to consider historical performance.
    *   Support for approximate route matching (e.g., based on the first 2 digits of French postal codes).
*   **Out of Scope:**
    *   Automated ETL pipeline for continuous GSheet synchronization as it is a one time activity.
    *   Complex AI/ML models for price prediction beyond statistical analysis of historical/current data.
    *   Direct modification or "injection" of historical data into current operational RFQ/bid tables.

## 2. Target Users
*   **Logistics Managers:** Primary users who will use the estimator for pre-RFQ quoting and benefit from enhanced provider matching.
*   **Sales/Account Managers:** Users who need quick, indicative pricing for deals.
*   **Administrators:** Users who might oversee the data import and system integrity.

## 3. User Stories

*   **US1 (Estimator):** As a Logistics Manager, I want to input an origin (country, city, postal code or a postal code prefix, i.e FR95 for Paris region, DE12 for Berlin region), destination (country, city, postal code or postal code prefix such as ES05 for Barcelona region), cargo type(s), equipment type, and weight, so that I can get an estimated price range (min, max, average, median) and price per kilometer based on historical bookings and recent similar RFQ bids.
*   **US2 (Estimator - Approximation):** As a Logistics Manager, when estimating for a route, I want the system to consider historical routes matching the first two digits of the postal code (e.g., FR95xxx) if an exact match isn't available, so I can get a relevant estimate even for new specific locations within a known region.
*   **US3 (Provider Matching):** As a Logistics Manager, when creating an RFQ, I want the system to highlight or rank providers who have successfully completed similar historical routes, so I can prioritize reliable partners.
*   **US4 (Data Integrity):** As an Administrator, I want to import historical data without affecting the quality or structure of my current operational RFQ and provider data.

## 4. Proposed Solution

### 4.1. High-Level Approach
1.  **Historical Data Storage:** Create dedicated tables within the Steelflow Supabase database to store the imported historical booked routes and their associated cargo types. These tables will be separate from the operational RFQ/bid tables.
2.  **One-Time Data Import:** The user will perform a one-time import of the cleaned GSheet data (exported as CSV) into these new historical tables using a database administration tool like pgAdmin.
3.  **Estimations Feature:**
    *   A new UI page/section ("Logistics Estimator").
    *   A new service (`estimation.service.ts`) will query both the `historical_booked_routes` and the current `rfq_bids` (for recent, relevant bids) to calculate price estimates.
    *   The service will support approximate matching for postal code prefixes (e.g., first 2 digits for France).
4.  **Enhanced Provider Matching:**
    *   The existing `rfq.service.ts` (`matchProvidersForRFQ` function) will be augmented to query the `historical_booked_routes` table.
    *   Providers identified from historical data for similar routes will be flagged or given a higher relevance score in the provider selection UI.

### 4.2. Data Flow for Estimations
User Inputs (Origin, Dest, Cargo, etc.) on Estimator Page
|
v
Estimator UI Component
|
v
getCombinedPriceEstimatesAction (new server action)
|
v
estimation.service.ts -> getCombinedPriceEstimates
| |
v v
Query historical_booked_routes 
Query rfq_bids (recent/relevant)
| |
----------------------------------
|
v
Aggregate, Calculate Stats (min, max, avg, median, price/km), Apply Approx. Matching
|
v
Return EstimationResult to UI
## 5. Detailed Features

### 5.1. Historical Data Integration
*   **5.1.1. Database Schema for Historical Data:**
    *   Table: `historical_bids`
        ```sql
        CREATE TABLE public.historical_bids (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            origin_country_id UUID REFERENCES public.countries(id) ON DELETE SET NULL, -- Nullable if mapping fails
            origin_country_raw TEXT, -- Store raw country name from CSV
            origin_city_raw TEXT,
            origin_postal_code_raw TEXT,
            origin_postal_prefix_fr CHAR(2), -- For FR, store first 2 digits
            destination_country_id UUID REFERENCES public.countries(id) ON DELETE SET NULL, -- Nullable
            destination_country_raw TEXT,
            destination_city_raw TEXT,
            destination_postal_code_raw TEXT,
            destination_postal_prefix_fr CHAR(2), -- For FR
            mapped_equipment_type_id UUID REFERENCES public.equipment_types(id) ON DELETE SET NULL, -- Nullable
            equipment_description_raw TEXT,
            weight_tonnes NUMERIC,
            booked_price NUMERIC,
            price_currency VARCHAR(3) DEFAULT 'EUR',
            provider_name_raw TEXT,
            mapped_provider_id UUID REFERENCES public.providers(id) ON DELETE SET NULL, -- Nullable
            booking_date DATE,
            price_per_km NUMERIC, -- Optional: Can be pre-calculated or calculated on the fly
            distance_km NUMERIC, -- Optional: If available in GSheet
            source_reference TEXT, -- e.g., "GSheet Row X"
            notes_raw TEXT,
            created_at TIMESTAMPTZ DEFAULT now(),
            updated_at TIMESTAMPTZ DEFAULT now()
        );

        -- Indexes for faster querying
        CREATE INDEX idx_hbr_origin_country ON public.historical_bids(origin_country_id);
        CREATE INDEX idx_hbr_destination_country ON public.historical_bids(destination_country_id);
        CREATE INDEX idx_hbr_origin_postal_prefix_fr ON public.historical_bids(origin_postal_prefix_fr);
        CREATE INDEX idx_hbr_destination_postal_prefix_fr ON public.historical_bids(destination_postal_prefix_fr);
        CREATE INDEX idx_hbr_equipment_type ON public.historical_bids(mapped_equipment_type_id);
        CREATE INDEX idx_hbr_provider ON public.historical_bids(mapped_provider_id);
        CREATE INDEX idx_hbr_booking_date ON public.historical_bids(booking_date);
        ```
    *   Table: `historical_route_cargo_types` (Junction Table)
        ```sql
        CREATE TABLE public.historical_bid_cargo_types (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            historical_route_id UUID NOT NULL REFERENCES public.historical_bids(id) ON DELETE CASCADE,
            mapped_cargo_type_id UUID REFERENCES public.cargo_types(id) ON DELETE SET NULL, -- Nullable
            cargo_description_item_raw TEXT NOT NULL, -- Store raw item description
            quantity INTEGER,
            created_at TIMESTAMPTZ DEFAULT now()
        );

        CREATE INDEX idx_hrct_historical_route_id ON public.historical_route_cargo_types(historical_route_id);
        CREATE INDEX idx_hrct_cargo_type_id ON public.historical_route_cargo_types(mapped_cargo_type_id);
        ```
*   **5.1.2. CSV Import Guidance:**
    *   Provide a template/example CSV structure matching the historical tables.
    *   User will need to pre-process their GSheet to fit this CSV structure.
    *   Key pre-processing:
        *   Split multiple cargo items for a single route into separate rows in a "cargo items" CSV or handle this during a more complex import script if the user is willing. (Simpler: one main cargo description per historical route, and details in `cargo_description_item_raw` of the junction table).
        *   Extract French postal code prefixes (`FRXX`) into a separate column if possible, or this can be done via a SQL `UPDATE` statement post-import.
    *   User imports data using pgAdmin's CSV import tool. `mapped_..._id` fields will initially be NULL or contain raw text if direct FK mapping isn't done in the CSV.
*   **5.1.3. Post-Import Mapping (Optional but Recommended - Server-Side Script/Job):**
    *   A one-time script (run by a dev) could attempt to populate `mapped_country_id`, `mapped_provider_id`, `mapped_equipment_type_id`, `mapped_cargo_type_id` by matching raw text fields against current Steelflow master data. This improves query performance and accuracy later.
        *   Example for `origin_postal_prefix_fr`: `UPDATE historical_booked_routes SET origin_postal_prefix_fr = SUBSTRING(origin_postal_code_raw FROM 1 FOR 2) WHERE origin_country_raw = 'France' AND origin_postal_code_raw ~ '^[0-9]{5}$';` (Adjust regex as needed).

### 5.2. Enhanced Estimations Page
*   **5.2.1. UI Inputs:**
    *   Origin: Country (dropdown), City (text), Postal Code / Prefix (text)
    *   Destination: Country (dropdown), City (text), Postal Code / Prefix (text)
    *   Cargo Type(s): Multi-select dropdown from `cargo_types`.
    *   Equipment Type: Dropdown from `equipment_types`.
    *   Weight (tonnes): Number input.
*   **5.2.2. Estimation Logic (`estimation.service.ts`):**
    *   Function `getCombinedPriceEstimates(params)`:
        *   Accepts UI inputs.
        *   Queries `historical_booked_routes` (joining `historical_route_cargo_types`).
            *   Filter by exact country matches.
            *   Filter by `mapped_equipment_type_id`.
            *   Filter by `mapped_cargo_type_id` (matching any of the selected cargo types).
            *   Filter by weight range (e.g., `params.weight_tonnes * 0.8` to `params.weight_tonnes * 1.2`).
            *   **Postal Code Matching:**
                *   If country is France and postal code input is 2 digits (e.g., "95"), query `WHERE origin_postal_prefix_fr = '95'`.
                *   If full postal code, try exact match on `origin_postal_code_raw`, then fall back to prefix match if no exact found.
                *   For other countries, try exact match on `_postal_code_raw`.
        *   Queries `rfq_bids` (joining `rfqs` and `rfq_cargo_types`).
            *   Apply similar filters.
            *   Consider only bids with status 'accepted' or recent 'bid_submitted' (e.g., last 3-6 months).
        *   Combine price points from both sources.
        *   Calculate: Min, Max, Average, Median price.
        *   Calculate: Min, Max, Average, Median Price/km (if `distance_km` is available or can be estimated via Google Maps API - *Note: Google Maps API calls can incur costs and add latency, consider if essential for v1*).
        *   Return statistics and `dataPointsCount`.
*   **5.2.3. UI Outputs:**
    *   Display calculated price statistics.
    *   Display number of data points used for the estimate (to indicate confidence).
    *   Optionally, show a breakdown: "X historical routes, Y recent bids."

### 5.3. Improved Provider Matching/Ranking
*   **5.3.1. Augment `rfq.service.ts` -> `matchProvidersForRFQ`:**
    *   After existing matching logic (based on `provider_routes`, `provider_equipments`), perform an additional query.
    *   Query `historical_booked_routes`:
        *   `SELECT mapped_provider_id, COUNT(*) as historical_bids_count FROM historical_booked_routes hbr JOIN historical_route_cargo_types hrct ON hbr.id = hrct.historical_route_id WHERE ... (filters similar to estimation logic, matching the current RFQ's details, including postal prefix logic) ... AND mapped_provider_id IS NOT NULL GROUP BY mapped_provider_id;`
    *   Merge this historical performance data with the list of operationally matched providers.
*   **5.3.2. UI Update in Provider Selection (`ProviderSelection.tsx` or similar):**
    *   For each matched provider, if they have `historical_bids_count > 0` for similar routes:
        *   Display a badge: "Experienced" or "✅ Historically Served Similar".
        *   Optionally, show the `historical_bids_count` on hover or in a detail column.
        *   This information can be used by the Logistics Manager to sort or prioritize providers.

## 6. Data Model (Recap of New Tables)
*   `public.historical_bids` (as defined in 5.1.1)
*   `public.historical_route_cargo_types` (as defined in 5.1.1)

## 7. Technical Considerations
*   **CSV Import:** The user is responsible for the quality of the CSV. The system will import what's given. Errors in the CSV (e.g., text in a numeric column) might cause pgAdmin import to fail or import bad data.
*   **Mapping Robustness:** Mapping raw text (e.g., `provider_name_raw`) to existing Steelflow IDs (`mapped_provider_id`) during query time can be complex and less performant than pre-mapped FKs. A post-import batch script to populate these `mapped_..._id` fields is highly recommended for better performance and data consistency, even if it's a one-time developer task.
*   **Performance:** Queries involving `LIKE 'XX%'` on postal prefixes or joining multiple tables for estimation will need appropriate database indexes on `historical_booked_routes` (e.g., on `origin_country_id`, `destination_country_id`, `origin_postal_prefix_fr`, `destination_postal_prefix_fr`, `mapped_equipment_type_id`, `mapped_provider_id`) and `historical_route_cargo_types` (`historical_route_id`, `mapped_cargo_type_id`).
*   **Scalability of Estimator:** If the number of historical routes and current bids grows very large, the estimation query might become slow. Consider optimizations like pre-aggregating some statistics if needed in the future.
*   **UI for Postal Prefix:** The Estimator UI needs to clearly indicate when a user can/should enter a full postal code vs. a prefix.

## 8. Success Metrics
*   **Estimator Usage:** Number of times the Logistics Estimator page is used per week.
*   **Estimation Accuracy (Qualitative):** Feedback from Logistics/Sales Managers on how well the estimates align with actual bid prices over time.
*   **Provider Selection:** Increase in selection of providers flagged as "historically experienced" for relevant RFQs.
*   **Time Savings:** Reduction in time spent by managers manually researching historical prices or suitable providers.

## 9. Future Considerations
*   More sophisticated mapping tools/UI for linking raw historical text to Steelflow entities.
*   Integration with a distance calculation service for more accurate price/km.
*   Weighting recent data more heavily in estimations.

## 10. Task Breakdown

### Phase 1: Historical Data Foundation
*   [ ] **DB:** Define and create `historical_booked_routes` table schema in Supabase.
*   [ ] **DB:** Define and create `historical_route_cargo_types` table schema in Supabase.
*   [ ] **DB:** Add necessary indexes to historical tables.
*   [ ] **DOC:** Prepare CSV template and import instructions for the user (for pgAdmin).
*   [ ] **DATA:** User performs one-time CSV import of ~300 historical routes.
*   [ ] **DEV (Optional but Recommended):** Develop a one-time server-side script to:
    *   [ ] Populate `origin_postal_prefix_fr` and `destination_postal_prefix_fr` from `_raw` fields.
    *   [ ] Attempt to map `_country_raw` to `_country_id`.
    *   [ ] Attempt to map `provider_name_raw` to `mapped_provider_id`.
    *   [ ] Attempt to map `equipment_description_raw` to `mapped_equipment_type_id`.
    *   [ ] Attempt to map `cargo_description_item_raw` to `mapped_cargo_type_id` in the junction table.

### Phase 2: Logistics Estimator Feature
*   [ ] **BE:** Create `estimation.service.ts`.
    *   [ ] Implement `getCombinedPriceEstimates(params)` function.
        *   [ ] Query `historical_booked_routes` with filters (country, equipment, cargo, weight, postal prefix).
        *   [ ] Query `rfq_bids` (recent/relevant) with similar filters.
        *   [ ] Combine price points.
        *   [ ] Calculate min/max/avg/median price and data points count.
*   [ ] **BE:** Create `getCombinedPriceEstimatesAction` server action in `rfq.actions.ts` (or a new `estimation.actions.ts`).
*   [ ] **FE:** Design and create new "Logistics Estimator" page (`/dashboard/estimator`).
    *   [ ] UI for input fields (origin, destination, cargo, equipment, weight).
    *   [ ] Logic to call the server action.
    *   [ ] Display estimation results (price stats, data points).
    *   [ ] Handle loading and error states.

### Phase 3: Enhanced Provider Matching
*   [ ] **BE:** Modify `rfq.service.ts` -> `matchProvidersForRFQ` function.
    *   [ ] Add logic to query `historical_booked_routes` for providers who served similar routes (based on RFQ details, including postal prefix).
    *   [ ] Return matched providers with an additional flag/score indicating historical experience (e.g., `historicalMatchCount`).
*   [ ] **FE:** Update Provider Selection UI (e.g., `ProviderSelection.tsx` or table in RFQ details).
    *   [ ] Display the historical experience flag/score for matched providers.
    *   [ ] Allow sorting/filtering by this new metric if applicable.

### Phase 4: Testing & Documentation
*   [ ] **DOC:** Update relevant module summaries (`rfqs_summary.md`).
*   [ ] **DOC:** User documentation for the new Estimator page.
