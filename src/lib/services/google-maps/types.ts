// Type for structured address components
export interface StructuredAddress {
  street_address: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
  country_code?: string;
  lat?: number;
  lng?: number;
}

// ==========================================
// Coordinate Resolution Types (PRD Task 1.4)
// ==========================================

/**
 * Result of coordinate resolution for a single address
 */
export interface CoordinateResolutionResult {
  /** Whether the geocoding was successful */
  success: boolean;
  /** Resolved latitude coordinate */
  lat?: number;
  /** Resolved longitude coordinate */
  lng?: number;
  /** Formatted address returned by Google Maps */
  formattedAddress?: string;
  /** Error message if geocoding failed */
  error?: string;
  /** Google Maps place ID for the resolved location */
  placeId?: string;
  /** Confidence level of the geocoding result */
  confidence?: 'ROOFTOP' | 'RANGE_INTERPOLATED' | 'GEOMETRIC_CENTER' | 'APPROXIMATE';
}

/**
 * Cached coordinates with metadata
 */
export interface CachedCoordinates {
  /** Latitude coordinate */
  lat: number;
  /** Longitude coordinate */
  lng: number;
  /** When the coordinates were resolved */
  resolvedAt: Date;
  /** Hash of the address used to resolve these coordinates */
  addressHash: string;
  /** Formatted address from geocoding */
  formattedAddress?: string;
}

/**
 * Input for batch coordinate resolution
 */
export interface AddressBatchInput {
  /** Unique identifier for this address in the batch */
  id: string;
  /** Full address string to geocode */
  address: string;
  /** Optional country code to improve geocoding accuracy */
  countryCode?: string;
}

/**
 * Result of batch coordinate resolution
 */
export interface BatchCoordinateResolutionResult {
  /** Results for each address in the batch */
  results: Array<{
    /** ID from the input */
    id: string;
    /** Coordinate resolution result */
    result: CoordinateResolutionResult;
  }>;
  /** Overall success rate of the batch */
  successRate: number;
  /** Total number of addresses processed */
  totalProcessed: number;
  /** Number of successful resolutions */
  successfulResolutions: number;
}

/**
 * Address change detection interface
 */
export interface AddressChangeDetection {
  /** Current hash of address components */
  currentHash: string;
  /** Previous hash (if any) */
  previousHash?: string;
  /** Whether the address has changed */
  hasChanged: boolean;
  /** Timestamp of the change detection */
  checkedAt: Date;
}

// ==========================================
// Distance Calculation Types (PRD Task 1.4)
// ==========================================

/**
 * Route coordinates for distance calculation
 */
export interface RouteCoordinates {
  /** Origin coordinates */
  origin: {
    lat: number;
    lng: number;
  };
  /** Destination coordinates */
  destination: {
    lat: number;
    lng: number;
  };
}

/**
 * Distance calculation result for a single route
 */
export interface DistanceCalculationResult {
  /** Whether the calculation was successful */
  success: boolean;
  /** Distance in kilometers */
  distanceKm?: number;
  /** Distance in meters (raw from API) */
  distanceMeters?: number;
  /** Duration in seconds */
  durationSeconds?: number;
  /** Human-readable distance text */
  distanceText?: string;
  /** Human-readable duration text */
  durationText?: string;
  /** Error message if calculation failed */
  error?: string;
  /** Status from Distance Matrix API */
  status?: string;
}

/**
 * Input for batch distance calculation
 */
export interface DistanceBatchInput {
  /** Unique identifier for this route in the batch */
  id: string;
  /** Route coordinates */
  route: RouteCoordinates;
}

/**
 * Result of batch distance calculation
 */
export interface BatchDistanceCalculationResult {
  /** Results for each route in the batch */
  results: Array<{
    /** ID from the input */
    id: string;
    /** Distance calculation result */
    result: DistanceCalculationResult;
  }>;
  /** Overall success rate of the batch */
  successRate: number;
  /** Total number of routes processed */
  totalProcessed: number;
  /** Number of successful calculations */
  successfulCalculations: number;
}

/**
 * Google Maps Distance Matrix API response types
 */
export interface DistanceMatrixResponse {
  status: string;
  origin_addresses: string[];
  destination_addresses: string[];
  rows: Array<{
    elements: Array<{
      status: string;
      distance?: {
        text: string;
        value: number;
      };
      duration?: {
        text: string;
        value: number;
      };
    }>;
  }>;
}

// ==========================================
// Route Visualization Types (PRD Task 3.1)
// ==========================================

/**
 * Route visualization result containing polyline and bounds data
 */
export interface RouteVisualizationResult {
  /** Whether the route calculation was successful */
  success: boolean;
  /** Encoded polyline string for the route */
  polyline?: string;
  /** Route bounds for map viewport optimization */
  bounds?: RouteBounds;
  /** Route legs with detailed information */
  legs?: RouteLeg[];
  /** Total distance in meters */
  distanceMeters?: number;
  /** Total duration in seconds */
  durationSeconds?: number;
  /** Human-readable distance text */
  distanceText?: string;
  /** Human-readable duration text */
  durationText?: string;
  /** Error message if route calculation failed */
  error?: string;
  /** Status from Routes API */
  status?: string;
}

/**
 * Route bounds for map viewport calculation
 */
export interface RouteBounds {
  /** Northeast corner of the bounds */
  northeast: {
    lat: number;
    lng: number;
  };
  /** Southwest corner of the bounds */
  southwest: {
    lat: number;
    lng: number;
  };
}

/**
 * Route leg information
 */
export interface RouteLeg {
  /** Start location */
  startLocation: {
    lat: number;
    lng: number;
  };
  /** End location */
  endLocation: {
    lat: number;
    lng: number;
  };
  /** Distance of this leg in meters */
  distanceMeters: number;
  /** Duration of this leg in seconds */
  durationSeconds: number;
  /** Human-readable distance text */
  distanceText: string;
  /** Human-readable duration text */
  durationText: string;
}

/**
 * Custom marker configuration for route visualization
 */
export interface RouteMarker {
  /** Marker position */
  position: {
    lat: number;
    lng: number;
  };
  /** Marker type */
  type: 'origin' | 'destination';
  /** Marker title/label */
  title: string;
  /** Optional custom icon configuration */
  icon?: {
    /** Icon URL or symbol */
    url?: string;
    /** Icon size */
    size?: {
      width: number;
      height: number;
    };
    /** Icon anchor point */
    anchor?: {
      x: number;
      y: number;
    };
  };
}

/**
 * Route styling configuration
 */
export interface RouteStyle {
  /** Polyline stroke color (hex) */
  strokeColor?: string;
  /** Polyline stroke weight in pixels */
  strokeWeight?: number;
  /** Polyline stroke opacity (0-1) */
  strokeOpacity?: number;
  /** Whether to show direction arrows */
  showDirectionArrows?: boolean;
}

/**
 * Map bounds calculation options
 */
export interface BoundsCalculationOptions {
  /** Padding around the route in pixels */
  padding?: number;
  /** Minimum zoom level */
  minZoom?: number;
  /** Maximum zoom level */
  maxZoom?: number;
}

/**
 * Route visualization request options
 */
export interface RouteVisualizationOptions {
  /** Travel mode (default: driving) */
  travelMode?: 'DRIVE' | 'WALK' | 'BICYCLE' | 'TRANSIT';
  /** Route preferences */
  routePreferences?: {
    /** Avoid tolls */
    avoidTolls?: boolean;
    /** Avoid highways */
    avoidHighways?: boolean;
    /** Avoid ferries */
    avoidFerries?: boolean;
  };
  /** Whether to include alternative routes */
  includeAlternatives?: boolean;
  /** Language for text responses */
  language?: string;
  /** Units for distance/duration text */
  units?: 'METRIC' | 'IMPERIAL';
}
