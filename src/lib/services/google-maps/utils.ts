/**
 * Google Maps Utilities
 *
 * Consolidated utility functions, classes, and constants for Google Maps services.
 * This file contains shared functionality used across coordinate resolution,
 * distance calculation, and route visualization services.
 *
 * Organized by domain:
 * - Coordinate Resolution Utilities
 * - Distance Calculation Utilities
 * - Route Visualization Utilities
 * - Shared Utilities
 */

import * as crypto from "crypto";
import {
  AddressChangeDetection,
  CoordinateResolutionResult,
  CachedCoordinates,
  RouteCoordinates,
  DistanceCalculationResult,
  RouteVisualizationResult,
  RouteBounds,
  RouteMarker,
  RouteStyle,
  BoundsCalculationOptions,
} from "./types";

// ==========================================
// COORDINATE RESOLUTION UTILITIES
// ==========================================

/**
 * Error class for coordinate resolution failures
 */
export class CoordinateResolutionError extends Error {
  constructor(
    message: string,
    public status?: string,
    public code?: string,
  ) {
    super(message);
    this.name = "CoordinateResolutionError";
  }
}

/**
 * Generates a hash for address components to detect changes
 * @param addressComponents Object containing address fields
 * @returns SHA-256 hash of the address components
 */
export function generateAddressHash(addressComponents: {
  originCity?: string;
  originAddress?: string;
  originPostalCode?: string;
  originCountryId?: string;
  destinationCity?: string;
  destinationAddress?: string;
  destinationPostalCode?: string;
  destinationCountryId?: string;
}): string {
  const {
    originCity = "",
    originAddress = "",
    originPostalCode = "",
    originCountryId = "",
    destinationCity = "",
    destinationAddress = "",
    destinationPostalCode = "",
    destinationCountryId = "",
  } = addressComponents;

  // Create a normalized string from all address components including country IDs
  const addressString = [
    originCity.trim().toLowerCase(),
    originAddress.trim().toLowerCase(),
    originPostalCode.trim().toLowerCase(),
    originCountryId.trim().toLowerCase(),
    destinationCity.trim().toLowerCase(),
    destinationAddress.trim().toLowerCase(),
    destinationPostalCode.trim().toLowerCase(),
    destinationCountryId.trim().toLowerCase(),
  ].join("|");

  return crypto.createHash("sha256").update(addressString).digest("hex");
}

/**
 * Detects if address components have changed by comparing hashes
 * @param currentAddressComponents Current address components
 * @param previousHash Previous address hash (if any)
 * @returns Address change detection result
 */
export function detectAddressChange(
  currentAddressComponents: {
    originCity?: string;
    originAddress?: string;
    originPostalCode?: string;
    originCountryId?: string;
    destinationCity?: string;
    destinationAddress?: string;
    destinationPostalCode?: string;
    destinationCountryId?: string;
  },
  previousHash?: string,
): AddressChangeDetection {
  const currentHash = generateAddressHash(currentAddressComponents);
  const hasChanged = previousHash ? currentHash !== previousHash : true;

  return {
    currentHash,
    previousHash,
    hasChanged,
    checkedAt: new Date(),
  };
}

/**
 * Normalizes an address string for consistent geocoding
 * @param address Raw address string
 * @param countryCode Optional country code for better accuracy
 * @returns Normalized address string
 */
export function normalizeAddress(address: string, countryCode?: string): string {
  // Basic normalization: trim whitespace and normalize spacing
  let normalized = address.trim().replace(/\s+/g, " ");

  // Add country code if provided and not already present
  if (countryCode && !normalized.toLowerCase().includes(countryCode.toLowerCase())) {
    normalized = `${normalized}, ${countryCode}`;
  }

  return normalized;
}

/**
 * Builds a geocodable address string from available address components
 * Uses a fallback strategy to maximize geocoding success with partial data
 *
 * @param addressComponents Object containing address components
 * @returns Geocodable address string
 */
export function buildGeocodableAddress(addressComponents: {
  address?: string | null;
  city?: string | null;
  postalCode?: string | null;
  countryCode?: string | null;
}): string {
  const parts: string[] = [];

  // Strategy 1: Full address if available
  if (addressComponents.address && addressComponents.address.trim()) {
    parts.push(addressComponents.address.trim());
  }

  // Strategy 2: Always include city if available (highly recommended)
  if (addressComponents.city && addressComponents.city.trim()) {
    parts.push(addressComponents.city.trim());
  }

  // Strategy 3: Include postal code if available (highly recommended for accuracy)
  if (addressComponents.postalCode && addressComponents.postalCode.trim()) {
    parts.push(addressComponents.postalCode.trim());
  }

  // Strategy 4: Include country code if available (helps with disambiguation)
  if (addressComponents.countryCode && addressComponents.countryCode.trim()) {
    parts.push(addressComponents.countryCode.trim());
  }

  const address = parts.join(", ");

  // Ensure we have at least city or postal code for meaningful geocoding
  if (!addressComponents.city && !addressComponents.postalCode) {
    throw new Error("Insufficient address data: at least city or postal code is required for geocoding");
  }

  return address;
}

/**
 * Creates cached coordinates object from resolution result
 * @param result Coordinate resolution result
 * @param addressHash Hash of the address used for resolution
 * @returns Cached coordinates object or null if resolution failed
 */
export function createCachedCoordinates(
  result: CoordinateResolutionResult,
  addressHash: string,
): CachedCoordinates | null {
  if (!result.success || result.lat === undefined || result.lng === undefined) {
    return null;
  }

  return {
    lat: result.lat,
    lng: result.lng,
    resolvedAt: new Date(),
    addressHash,
    formattedAddress: result.formattedAddress,
  };
}

// ==========================================
// DISTANCE CALCULATION UTILITIES
// ==========================================

/**
 * Error class for distance calculation failures
 */
export class DistanceCalculationError extends Error {
  constructor(
    message: string,
    public status?: string,
    public code?: string,
  ) {
    super(message);
    this.name = "DistanceCalculationError";
  }
}

/**
 * Estimates the cost of distance calculations based on Google Maps pricing
 * @param numberOfRoutes Number of routes to calculate
 * @returns Estimated cost in USD
 */
export function estimateDistanceCalculationCost(numberOfRoutes: number): number {
  // Google Distance Matrix API pricing (as of 2024)
  // $5.00 per 1000 elements for standard usage
  const costPer1000Elements = 5.0;
  return (numberOfRoutes / 1000) * costPer1000Elements;
}

/**
 * Validates distance calculation result
 * @param result Distance calculation result to validate
 * @returns True if result is valid
 */
export function validateDistanceResult(result: DistanceCalculationResult): boolean {
  return (
    result.success &&
    typeof result.distanceKm === "number" &&
    result.distanceKm > 0 &&
    typeof result.durationSeconds === "number" &&
    result.durationSeconds > 0
  );
}

// ==========================================
// ROUTE VISUALIZATION UTILITIES
// ==========================================

/**
 * Default route styling configuration
 */
export const DEFAULT_ROUTE_STYLE: RouteStyle = {
  strokeColor: "#2563eb", // Blue-600 from Tailwind
  strokeWeight: 4,
  strokeOpacity: 0.8,
  showDirectionArrows: true,
};

/**
 * Default bounds calculation options
 */
export const DEFAULT_BOUNDS_OPTIONS: BoundsCalculationOptions = {
  padding: 50,
  minZoom: 5,
  maxZoom: 18,
};

/**
 * Formats distance in meters to human-readable text
 * @param meters Distance in meters
 * @param units Unit system to use
 * @returns Formatted distance string
 */
export function formatDistance(meters: number, units: "METRIC" | "IMPERIAL" = "METRIC"): string {
  if (units === "IMPERIAL") {
    const miles = meters * 0.000621371;
    if (miles < 1) {
      const feet = meters * 3.28084;
      return `${Math.round(feet)} ft`;
    }
    return `${miles.toFixed(1)} mi`;
  } else {
    if (meters < 1000) {
      return `${Math.round(meters)} m`;
    }
    const km = meters / 1000;
    return `${km.toFixed(1)} km`;
  }
}

/**
 * Formats duration in seconds to human-readable text
 * @param seconds Duration in seconds
 * @returns Formatted duration string
 */
export function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);

  if (hours > 0) {
    return `${hours}h ${minutes}m`;
  } else if (minutes > 0) {
    return `${minutes}m`;
  } else {
    return `${seconds}s`;
  }
}

/**
 * Generates route markers for origin and destination
 * @param route Route coordinates
 * @param options Marker customization options
 * @returns Array of route markers
 */
export function generateRouteMarkers(
  route: RouteCoordinates,
  options: {
    originTitle?: string;
    destinationTitle?: string;
    customIcons?: boolean;
  } = {},
): RouteMarker[] {
  const {
    originTitle = "Origin",
    destinationTitle = "Destination",
    customIcons = true,
  } = options;

  const markers: RouteMarker[] = [
    {
      position: route.origin,
      type: "origin",
      title: originTitle,
    },
    {
      position: route.destination,
      type: "destination",
      title: destinationTitle,
    },
  ];

  // Add custom icons if requested
  if (customIcons) {
    markers[0].icon = {
      url: "data:image/svg+xml;base64," + btoa(`
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="12" cy="12" r="8" fill="#10b981" stroke="#ffffff" stroke-width="2"/>
          <circle cx="12" cy="12" r="3" fill="#ffffff"/>
        </svg>
      `),
      size: { width: 24, height: 24 },
      anchor: { x: 12, y: 12 },
    };

    markers[1].icon = {
      url: "data:image/svg+xml;base64," + btoa(`
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="12" cy="12" r="8" fill="#ef4444" stroke="#ffffff" stroke-width="2"/>
          <circle cx="12" cy="12" r="3" fill="#ffffff"/>
        </svg>
      `),
      size: { width: 24, height: 24 },
      anchor: { x: 12, y: 12 },
    };
  }

  return markers;
}

/**
 * Calculates optimal map bounds for route visualization
 * @param route Route coordinates
 * @param options Bounds calculation options
 * @returns Calculated route bounds
 */
export function calculateOptimalBounds(
  route: RouteCoordinates,
  options: BoundsCalculationOptions = DEFAULT_BOUNDS_OPTIONS,
): RouteBounds {
  const { padding = 50 } = options;

  // Calculate the bounds that encompass both origin and destination
  const latitudes = [route.origin.lat, route.destination.lat];
  const longitudes = [route.origin.lng, route.destination.lng];

  const minLat = Math.min(...latitudes);
  const maxLat = Math.max(...latitudes);
  const minLng = Math.min(...longitudes);
  const maxLng = Math.max(...longitudes);

  // Add padding (convert pixels to approximate degrees)
  // This is a rough approximation - in a real implementation you'd want more precise calculations
  const latPadding = (padding / 111000) * 1.5; // ~111km per degree latitude
  const lngPadding = (padding / (111000 * Math.cos(minLat * Math.PI / 180))) * 1.5;

  return {
    southwest: {
      lat: minLat - latPadding,
      lng: minLng - lngPadding,
    },
    northeast: {
      lat: maxLat + latPadding,
      lng: maxLng + lngPadding,
    },
  };
}

/**
 * Estimates the cost of route visualization API calls
 * @param numberOfRoutes Number of routes to visualize
 * @returns Estimated cost in USD
 */
export function estimateRouteVisualizationCost(numberOfRoutes: number): number {
  // Google Routes API pricing (as of 2024)
  // $5.00 per 1000 requests for basic routing
  const costPer1000Requests = 5.0;
  return (numberOfRoutes / 1000) * costPer1000Requests;
}

/**
 * Validates route visualization result
 * @param result Route visualization result to validate
 * @returns Whether the result is valid and complete
 */
export function validateRouteVisualizationResult(
  result: RouteVisualizationResult,
): boolean {
  if (!result.success) {
    return false;
  }

  // Check required fields for successful result
  return !!(
    result.polyline &&
    result.distanceMeters !== undefined &&
    result.durationSeconds !== undefined &&
    result.legs &&
    result.legs.length > 0
  );
}

// ==========================================
// SHARED UTILITIES
// ==========================================

/**
 * Validates coordinate values
 * @param lat Latitude value
 * @param lng Longitude value
 * @returns True if coordinates are valid
 */
export function validateCoordinates(lat: number, lng: number): boolean {
  return (
    typeof lat === "number" &&
    typeof lng === "number" &&
    !isNaN(lat) &&
    !isNaN(lng) &&
    lat >= -90 &&
    lat <= 90 &&
    lng >= -180 &&
    lng <= 180
  );
}

/**
 * Validates route coordinates before making API calls
 * @param route Route coordinates to validate
 * @returns Whether the coordinates are valid
 */
export function validateRouteCoordinates(route: RouteCoordinates): boolean {
  return (
    validateCoordinates(route.origin.lat, route.origin.lng) &&
    validateCoordinates(route.destination.lat, route.destination.lng)
  );
}
