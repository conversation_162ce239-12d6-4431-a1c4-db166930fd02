/**
 * Utility functions for equipment-related operations
 */

/**
 * Get theme-aware color classes for an equipment category
 * @param category The equipment category
 * @returns Object with bg, text, and border color classes that adapt to light/dark themes
 */
export const getEquipmentColor = (
  category: string,
): { bg: string; text: string; border: string } => {
  switch (category.toLowerCase()) {
    case "trailer":
      return {
        bg: "bg-equipment-trailer",
        text: "text-equipment-trailer-foreground",
        border: "border-equipment-trailer-foreground/20",
      };
    case "container":
      return {
        bg: "bg-equipment-container",
        text: "text-equipment-container-foreground",
        border: "border-equipment-container-foreground/20",
      };
    case "vehicle":
    case "truck":
      return {
        bg: "bg-equipment-vehicle",
        text: "text-equipment-vehicle-foreground",
        border: "border-equipment-vehicle-foreground/20",
      };
    case "chassis":
      return {
        bg: "bg-equipment-chassis",
        text: "text-equipment-chassis-foreground",
        border: "border-equipment-chassis-foreground/20",
      };
    default:
      return {
        bg: "bg-equipment-default",
        text: "text-equipment-default-foreground",
        border: "border-equipment-default-foreground/20",
      };
  }
};
