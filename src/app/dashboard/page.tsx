import { Suspense } from "react";
import { Metadata } from "next";
import Link from "next/link";
import {
  FileText,
  Users,
  Package,
  Truck,
  ArrowRight,
  Clock,
} from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { H1, H4, Lead, Text, SmallText } from "@/components/ui/typography";
import {
  getProviderCountsAction,
  getProvidersWithEquipmentAction,
} from "@/lib/actions/provider.actions";
import { getRFQsAction } from "@/lib/actions/rfq.actions";
import { getCargo } from "@/lib/actions/cargo";
import { getEquipment } from "@/lib/actions/equipment";

export const metadata: Metadata = {
  title: "Dashboard | Steelflow",
  description: "Steelflow logistics management dashboard",
};

// Dashboard stats component
function DashboardStats({
  rfqCount,
  providerCount,
  cargoCount,
  equipmentCount,
}: {
  rfqCount: number;
  providerCount: number;
  cargoCount: number;
  equipmentCount: number;
}) {
  const stats = [
    {
      title: "RFQs",
      value: rfqCount,
      description: "Total request for quotes",
      icon: FileText,
      color: "text-primary",
      bgColor: "bg-primary/10",
      href: "/dashboard/rfqs",
    },
    {
      title: "Providers",
      value: providerCount,
      description: "Transport providers",
      icon: Users,
      color: "text-primary",
      bgColor: "bg-primary/10",
      href: "/dashboard/providers",
    },
    {
      title: "Cargo",
      value: cargoCount,
      description: "Cargo types",
      icon: Package,
      color: "text-primary",
      bgColor: "bg-primary/10",
      href: "/dashboard/cargo",
    },
    {
      title: "Equipment",
      value: equipmentCount,
      description: "Equipment types",
      icon: Truck,
      color: "text-primary",
      bgColor: "bg-primary/10",
      href: "/dashboard/equipment",
    },
  ];

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
      {stats.map((stat) => (
        <Card key={stat.title} className="overflow-hidden">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center justify-between">
              <H4 className="m-0">{stat.title}</H4>
              <div className={`p-1.5 ${stat.bgColor} rounded-full`}>
                <stat.icon className={`h-5 w-5 ${stat.color}`} />
              </div>
            </CardTitle>
            <CardDescription>{stat.description}</CardDescription>
          </CardHeader>
          <CardContent>
            <Text className="text-3xl font-bold">{stat.value}</Text>
          </CardContent>
          <CardFooter className="pt-0">
            <Button variant="ghost" size="sm" className="px-0" asChild>
              <Link href={stat.href}>
                View all
                <ArrowRight className="ml-1 h-4 w-4" />
              </Link>
            </Button>
          </CardFooter>
        </Card>
      ))}
    </div>
  );
}

// Recent RFQs component
function RecentRFQs({ rfqs }: { rfqs: any[] }) {
  return (
    <Card className="mb-8">
      <CardHeader>
        <CardTitle className="flex items-center">
          <H4 className="m-0">Recent RFQs</H4>
          <Clock className="ml-2 h-5 w-5 text-muted-foreground" />
        </CardTitle>
        <CardDescription>Recently created request for quotes</CardDescription>
      </CardHeader>
      <CardContent>
        {rfqs.length === 0 ? (
          <SmallText className="text-center py-4">
            No RFQs found
          </SmallText>
        ) : (
          <div className="space-y-4">
            {rfqs.map((rfq) => (
              <div
                key={rfq.id}
                className="flex items-center justify-between border-b pb-3"
              >
                <div>
                  <Text className="font-medium">
                    RFQ #{rfq.sequence_number || rfq.id.substring(0, 8)}
                  </Text>
                  <SmallText className="flex items-center">
                    <span className="capitalize">{rfq.status}</span>
                    <span className="mx-2">•</span>
                    <span>{new Date(rfq.created_at).toLocaleDateString()}</span>
                  </SmallText>
                </div>
                <Button variant="outline" size="sm" asChild>
                  <Link href={`/dashboard/rfqs/${rfq.id}`}>View</Link>
                </Button>
              </div>
            ))}
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button variant="outline" asChild>
          <Link href="/dashboard/rfqs">
            View all RFQs
            <ArrowRight className="ml-2 h-4 w-4" />
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );
}

// Recent Providers component
function RecentProviders({ providers }: { providers: any[] }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <H4 className="m-0">Active Providers</H4>
          <Users className="ml-2 h-5 w-5 text-muted-foreground" />
        </CardTitle>
        <CardDescription>Your active transport providers</CardDescription>
      </CardHeader>
      <CardContent>
        {providers.length === 0 ? (
          <SmallText className="text-center py-4">
            No active providers found
          </SmallText>
        ) : (
          <div className="space-y-4">
            {providers.map((provider) => (
              <div
                key={provider.id}
                className="flex items-center justify-between border-b pb-3"
              >
                <div>
                  <Text className="font-medium">{provider.name}</Text>
                  <SmallText className="flex items-center">
                    <span>{provider.email}</span>
                    {provider.equipments && provider.equipments.length > 0 && (
                      <>
                        <span className="mx-2">•</span>
                        <span>
                          {provider.equipments.length} equipment types
                        </span>
                      </>
                    )}
                  </SmallText>
                </div>
                <Button variant="outline" size="sm" asChild>
                  <Link href={`/dashboard/providers/${provider.id}`}>View</Link>
                </Button>
              </div>
            ))}
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button variant="outline" asChild>
          <Link href="/dashboard/providers">
            View all providers
            <ArrowRight className="ml-2 h-4 w-4" />
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );
}

// Dashboard content with data fetching
async function DashboardContent() {
  // Fetch data for dashboard
  const [
    rfqsRes,
    providerCountsRes,
    activeProvidersRes,
    cargoRes,
    equipmentRes,
  ] = await Promise.all([
    getRFQsAction(),
    getProviderCountsAction(),
    getProvidersWithEquipmentAction({
      page: 1,
      pageSize: 5,
    }),
    getCargo(),
    getEquipment(),
  ]);

  // Extract data and handle errors
  const rfqs = rfqsRes.success ? rfqsRes.data.data : [];
  const providerCounts = providerCountsRes.success
    ? providerCountsRes.data
    : {
        totalCount: 0,
        activeCount: 0,
        pendingCount: 0,
        inactiveCount: 0,
        verifiedCount: 0,
      };
  const activeProviders = activeProvidersRes.success
    ? activeProvidersRes.data
    : [];
  const cargo = cargoRes.data || [];
  const equipment = equipmentRes.data || [];

  return (
    <>
      <div className="mb-8">
        <H1>Dashboard</H1>
        <Lead>
          Welcome to your Steelflow logistics management dashboard
        </Lead>
      </div>

      <DashboardStats
        rfqCount={
          rfqsRes.success ? rfqsRes.data.pagination?.totalCount || 0 : 0
        }
        providerCount={providerCounts.totalCount}
        cargoCount={cargo.length}
        equipmentCount={equipment.length}
      />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <RecentRFQs rfqs={rfqs} />
        <RecentProviders providers={activeProviders} />
      </div>
    </>
  );
}

// Dashboard skeleton loader
function DashboardSkeleton() {
  return (
    <>
      <div className="mb-8">
        <Skeleton className="h-10 w-[250px] mb-2" />
        <Skeleton className="h-5 w-[350px]" />
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="overflow-hidden">
            <CardHeader className="pb-2">
              <Skeleton className="h-6 w-[100px] mb-2" />
              <Skeleton className="h-4 w-[150px]" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-10 w-[80px]" />
            </CardContent>
            <CardFooter className="pt-0">
              <Skeleton className="h-4 w-[100px]" />
            </CardFooter>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="mb-8">
          <CardHeader>
            <Skeleton className="h-6 w-[150px] mb-2" />
            <Skeleton className="h-4 w-[200px]" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div
                  key={i}
                  className="flex items-center justify-between border-b pb-3"
                >
                  <div>
                    <Skeleton className="h-5 w-[150px] mb-2" />
                    <Skeleton className="h-4 w-[200px]" />
                  </div>
                  <Skeleton className="h-9 w-[80px]" />
                </div>
              ))}
            </div>
          </CardContent>
          <CardFooter>
            <Skeleton className="h-10 w-[150px]" />
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-[150px] mb-2" />
            <Skeleton className="h-4 w-[200px]" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div
                  key={i}
                  className="flex items-center justify-between border-b pb-3"
                >
                  <div>
                    <Skeleton className="h-5 w-[150px] mb-2" />
                    <Skeleton className="h-4 w-[200px]" />
                  </div>
                  <Skeleton className="h-9 w-[80px]" />
                </div>
              ))}
            </div>
          </CardContent>
          <CardFooter>
            <Skeleton className="h-10 w-[150px]" />
          </CardFooter>
        </Card>
      </div>
    </>
  );
}

export default function DashboardPage() {
  return (
    <Suspense fallback={<DashboardSkeleton />}>
      <DashboardContent />
    </Suspense>
  );
}
