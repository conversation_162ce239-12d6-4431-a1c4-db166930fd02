"use client";

import React, { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import {
  Loader2,
  Mail,
  ChevronDown,
  ChevronUp,
  AlertCircle,
  CheckCircle,
  Clock,
} from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { getRFQEmailsAction } from "@/lib/actions/rfq.actions";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("EmailHistory");

interface EmailHistoryProps {
  rfqId: string;
}

// Use the actual type returned by getRFQEmailsAction
type Email = {
  id: string;
  rfq_id: string;
  provider_id: string;
  subject: string;
  body: string;
  sent_at: string;
  status: string;
  provider?: { name: string };
};

export function EmailHistory({ rfqId }: EmailHistoryProps) {
  const [selectedEmail, setSelectedEmail] = useState<Email | null>(null);
  const [expandedEmailId, setExpandedEmailId] = useState<string | null>(null);

  // Use TanStack Query for data fetching with cache invalidation support
  const {
    data: emails = [],
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ["rfq-emails", rfqId],
    queryFn: async () => {
      logger.info("Fetching RFQ emails", { rfqId });
      const result = await getRFQEmailsAction(rfqId);
      if (!result.success) {
        throw new Error(result.error || "Failed to fetch RFQ emails");
      }
      return result.data;
    },
    staleTime: 30000, // 30 seconds
    gcTime: 300000, // 5 minutes
    retry: 2,
    retryDelay: 1000,
    enabled: !!rfqId,
  });

  // Helper function to get status badge for RFQ emails
  const getStatusBadge = (email: Email) => {
    // RFQ emails only have a basic status field
    switch (email.status) {
      case "sent":
        return (
          <Badge
            variant="outline"
            className="bg-green-50 text-green-700 border-green-200 flex items-center gap-1"
          >
            <CheckCircle className="h-3 w-3" />
            Sent
          </Badge>
        );
      case "pending":
        return (
          <Badge
            variant="outline"
            className="bg-yellow-50 text-yellow-700 border-yellow-200 flex items-center gap-1"
          >
            <Clock className="h-3 w-3" />
            Pending
          </Badge>
        );
      case "failed":
        return (
          <Badge
            variant="outline"
            className="bg-red-50 text-red-700 border-red-200 flex items-center gap-1"
          >
            <AlertCircle className="h-3 w-3" />
            Failed
          </Badge>
        );
      default:
        return (
          <Badge
            variant="outline"
            className="bg-muted text-muted-foreground border-muted-foreground/20 flex items-center gap-1"
          >
            <Mail className="h-3 w-3" />
            {email.status}
          </Badge>
        );
    }
  };

  // Handle error state
  if (isError) {
    logger.error("Error fetching emails:", error);
    return (
      <div className="flex justify-center p-8">
        <div className="text-center space-y-4">
          <p className="text-muted-foreground">Failed to load email history</p>
          <p className="text-sm text-red-600">
            {error instanceof Error ? error.message : "Unknown error"}
          </p>
        </div>
      </div>
    );
  }

  const handleViewEmail = (email: Email) => {
    setSelectedEmail(email);
  };

  const toggleExpandEmail = (emailId: string) => {
    if (expandedEmailId === emailId) {
      setExpandedEmailId(null);
    } else {
      setExpandedEmailId(emailId);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    );
  }

  if (emails.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center text-muted-foreground">
          No emails have been sent for this RFQ yet.
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <Table className="border rounded-md overflow-hidden">
        <TableHeader>
          <TableRow className="bg-muted/30">
            <TableHead className="w-12"></TableHead>
            <TableHead>Recipient</TableHead>
            <TableHead>Subject</TableHead>
            <TableHead>Sent Date</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {emails.map((email) => (
            <React.Fragment key={email.id}>
              <TableRow>
                <TableCell className="px-4 py-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => toggleExpandEmail(email.id)}
                    className="p-0 h-8 w-8"
                  >
                    {expandedEmailId === email.id ? (
                      <ChevronUp className="h-4 w-4" />
                    ) : (
                      <ChevronDown className="h-4 w-4" />
                    )}
                  </Button>
                </TableCell>
                <TableCell className="px-4 py-2">
                  <div className="font-medium">
                    {email.provider?.name || "Unknown Provider"}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Provider ID: {email.provider_id}
                  </div>
                </TableCell>
                <TableCell className="px-4 py-2">{email.subject}</TableCell>
                <TableCell className="px-4 py-2">
                  {email.sent_at
                    ? format(new Date(email.sent_at), "MMM d, yyyy h:mm a")
                    : "Not sent"}
                </TableCell>
                <TableCell className="px-4 py-2">
                  {getStatusBadge(email)}
                </TableCell>
                <TableCell className="px-4 py-2 text-right">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleViewEmail(email)}
                    className="h-8"
                  >
                    <Mail className="h-4 w-4 mr-2" />
                    View
                  </Button>
                </TableCell>
              </TableRow>
              {expandedEmailId === email.id && (
                <TableRow key={`${email.id}-expanded`}>
                  <TableCell colSpan={6} className="px-4 py-2 bg-muted/20">
                    <div className="whitespace-pre-wrap p-3 rounded-md">
                      {email.body}
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </React.Fragment>
          ))}
        </TableBody>
      </Table>

      {/* Email View Dialog */}
      <Dialog
        open={!!selectedEmail}
        onOpenChange={(open) => !open && setSelectedEmail(null)}
      >
        <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{selectedEmail?.subject}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 mt-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">To:</span>{" "}
                <span>
                  {selectedEmail?.provider?.name || "Unknown Provider"}
                </span>
              </div>
              <div>
                <span className="font-medium">Sent:</span>{" "}
                <span>
                  {selectedEmail?.sent_at
                    ? format(
                        new Date(selectedEmail.sent_at),
                        "MMM d, yyyy h:mm a",
                      )
                    : "Not sent"}
                </span>
              </div>
            </div>

            {/* Status Section */}
            {selectedEmail && (
              <div className="space-y-2 mt-4 border p-4 rounded-md bg-muted/10">
                <h3 className="text-sm font-medium">Email Status</h3>
                <div className="flex items-center gap-2 text-sm">
                  <span className="text-muted-foreground">Status:</span>
                  {getStatusBadge(selectedEmail)}
                </div>
              </div>
            )}

            <div className="border-t pt-4 mt-4">
              <div className="whitespace-pre-wrap">{selectedEmail?.body}</div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
