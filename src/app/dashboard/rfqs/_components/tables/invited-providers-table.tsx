"use client";

import { useMemo } from "react";
import { ColumnDef } from "@tanstack/react-table";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Euro, ArrowUpDown, Bot } from "lucide-react";
import { DataTable } from "@/components/ui/tables";
import { InvitedProviderForTable, RFQBid } from "@/lib/schemas";
import { formatDistanceToNow } from "date-fns";
import { Skeleton } from "@/components/ui/skeleton";

// Type for bid with provider name for display in the dialog
export type BidWithProviderName = RFQBid & { provider_name?: string };

/**
 * Loading skeleton for the invited providers table
 * Provides a consistent loading experience while data is being fetched
 */
function InvitedProvidersTableSkeleton() {
  return (
    <div className="space-y-4">
      {/* Table header skeleton */}
      <div className="flex items-center justify-between">
        <Skeleton className="h-8 w-48" />
        <Skeleton className="h-8 w-32" />
      </div>

      {/* Table rows skeleton */}
      <div className="space-y-3">
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="flex items-center space-x-4 p-4 border rounded-lg">
            <Skeleton className="h-4 w-32" /> {/* Provider name */}
            <Skeleton className="h-4 w-24" /> {/* Invited date */}
            <Skeleton className="h-6 w-20" /> {/* Status badge */}
            <Skeleton className="h-4 w-28" /> {/* Bid amount */}
            <Skeleton className="h-8 w-16" /> {/* Action button */}
          </div>
        ))}
      </div>

      {/* Pagination skeleton */}
      <div className="flex items-center justify-between">
        <Skeleton className="h-4 w-32" />
        <div className="flex items-center space-x-2">
          <Skeleton className="h-8 w-8" />
          <Skeleton className="h-8 w-8" />
          <Skeleton className="h-8 w-8" />
          <Skeleton className="h-8 w-8" />
        </div>
      </div>
    </div>
  );
}

/**
 * Error fallback component for when data fetching fails
 */
function InvitedProvidersTableError() {
  return (
    <Card>
      <CardContent className="p-6 text-center text-destructive">
        <p className="mb-2">Failed to load invited providers</p>
        <p className="text-sm text-muted-foreground">
          Please try refreshing the page or contact support if the problem persists.
        </p>
      </CardContent>
    </Card>
  );
}

// Render provider status badge
export const renderResponseBadge = (status: string) => {
  if (status === "bid_submitted") {
    return (
      <Badge
        variant="default"
        className="bg-green-100 text-green-800 border-green-200"
      >
        Bid Received
      </Badge>
    );
  } else if (status === "declined") {
    return <Badge variant="destructive">Declined</Badge>;
  } else {
    return (
      <Badge variant="outline" className="bg-muted/50">
        Awaiting Response
      </Badge>
    );
  }
};

/**
 * Creates column definitions for the invited providers table
 * Includes sorting, filtering, and proper visual indicators
 */
function createInvitedProvidersColumns(
  handleAddBid: (providerId: string, providerName: string) => void,
  handleUpdateBid: (bid: BidWithProviderName) => void,
  rfqStatus: string,
): ColumnDef<InvitedProviderForTable>[] {
  return [
    {
      id: "provider_name",
      accessorFn: (row) => row.provider?.name,
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-semibold hover:bg-transparent"
        >
          Provider
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const provider = row.original.provider;
        return (
          <div className="font-medium">
            {provider?.name || "Unknown Provider"}
          </div>
        );
      },
      enableSorting: true,
      enableGlobalFilter: true,
      filterFn: "includesString",
    },
    {
      accessorKey: "invited_at",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-semibold hover:bg-transparent"
        >
          Invited
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const invitedAt = row.original.invited_at;
        if (!invitedAt) return <span className="text-muted-foreground">N/A</span>;

        try {
          const date = new Date(invitedAt);
          const relativeTime = formatDistanceToNow(date, { addSuffix: true });
          const formattedDate = date.toLocaleDateString();

          return (
            <div
              className="text-sm whitespace-nowrap cursor-help"
              title={`${relativeTime} (${formattedDate})`}
            >
              {relativeTime}
            </div>
          );
        } catch {
          return <span className="text-muted-foreground">Invalid date</span>;
        }
      },
      enableSorting: true,
      sortingFn: (rowA, rowB) => {
        const a = rowA.original.invited_at;
        const b = rowB.original.invited_at;
        if (!a && !b) return 0;
        if (!a) return 1;
        if (!b) return -1;
        return new Date(a).getTime() - new Date(b).getTime();
      },
    },
    {
      accessorKey: "status",
      header: "Response",
      cell: ({ row }) => {
        return renderResponseBadge(row.original.status);
      },
      enableSorting: true,
      enableGlobalFilter: true,
    },
    {
      id: "bid_price",
      accessorFn: (row) => row.bid?.price,
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-semibold hover:bg-transparent"
        >
          Bid Amount (EUR)
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const bid = row.original.bid;

        if (bid) {
          return (
            <div className="flex items-center font-medium text-green-700">
              <Euro className="h-4 w-4 mr-1" />
              {bid.price.toLocaleString()}
              {bid.is_ai_extracted && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Bot className="h-4 w-4 ml-2 text-blue-600" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>AI-extracted bid</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
          );
        }
        return <span className="text-muted-foreground">—</span>;
      },
      enableSorting: true,
      sortingFn: (rowA, rowB) => {
        const a = rowA.original.bid?.price;
        const b = rowB.original.bid?.price;
        if (a === undefined && b === undefined) return 0;
        if (a === undefined) return 1;
        if (b === undefined) return -1;
        return a - b;
      },
    },
    {
      id: "bid_notes",
      accessorFn: (row) => row.bid?.notes,
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-semibold hover:bg-transparent"
        >
          Notes
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const bid = row.original.bid;
        const notes = bid?.notes;

        if (notes && notes.trim()) {
          // Truncate long notes for display
          const truncatedNotes = notes.length > 50
            ? `${notes.substring(0, 50)}...`
            : notes;

          return (
            <div className="max-w-[200px]">
              <span
                className="text-sm text-muted-foreground cursor-help"
                title={notes}
              >
                {truncatedNotes}
              </span>
            </div>
          );
        }

        return <span className="text-muted-foreground">—</span>;
      },
      enableSorting: true,
      enableGlobalFilter: true,
      filterFn: "includesString",
      sortingFn: (rowA, rowB) => {
        const a = rowA.original.bid?.notes || "";
        const b = rowB.original.bid?.notes || "";
        return a.localeCompare(b);
      },
    },
    {
      id: "actions",
      header: () => <div className="text-right">Actions</div>,
      cell: ({ row }) => {
        const rfqProvider = row.original;
        const bid = rfqProvider.bid;
        const hasBid = !!bid;

        return (
          <div className="text-right">
            {hasBid ? (
              // Show "Update Bid" button when bid exists
              <Button
                variant="outline"
                size="sm"
                className="h-8 px-2 text-xs"
                disabled={rfqStatus === "draft"}
                onClick={() => {
                  // Add provider_name to the bid for display in the dialog
                  const bidWithProviderName = {
                    ...bid,
                    provider_name: rfqProvider.provider?.name,
                  };
                  handleUpdateBid(bidWithProviderName);
                }}
              >
                Update Bid
              </Button>
            ) : (
              // Show "Add Bid" button when no bid exists and provider hasn't submitted a bid
              rfqProvider.status !== "bid_submitted" && (
                <Button
                  variant="outline"
                  size="sm"
                  className="h-8 px-2 text-xs"
                  disabled={rfqStatus === "draft"}
                  onClick={() => {
                    handleAddBid(
                      rfqProvider.provider_id,
                      rfqProvider.provider?.name || "Provider",
                    );
                  }}
                >
                  Add Bid
                </Button>
              )
            )}
          </div>
        );
      },
      enableSorting: false,
    },
  ];
}

interface InvitedProvidersTableProps {
  rfqId: string;
  rfqStatus: string;
  onAddBid: (providerId: string, providerName: string) => void;
  onUpdateBid: (bid: BidWithProviderName) => void;
  initialInvitedProviders?: any[];
}

/**
 * Internal component that handles data fetching and rendering
 * Uses initial data from server component to avoid render-time state updates
 */
function InvitedProvidersTableContent({
  rfqStatus,
  onAddBid,
  onUpdateBid,
  initialInvitedProviders = [],
}: Omit<InvitedProvidersTableProps, 'rfqId'>) {
  // Use initial data from server component instead of TanStack Query hook
  // This prevents the "Cannot update component during render" error
  const invitedProviders = initialInvitedProviders;
  const isLoading = false;
  const isError = false;

  // Memoize column definitions to prevent unnecessary re-renders
  const columns = useMemo(
    () => createInvitedProvidersColumns(onAddBid, onUpdateBid, rfqStatus),
    [onAddBid, onUpdateBid, rfqStatus]
  );

  // Handle loading state
  if (isLoading) {
    return <InvitedProvidersTableSkeleton />;
  }

  // Handle error state
  if (isError) {
    return <InvitedProvidersTableError />;
  }

  // Handle empty state
  if (invitedProviders.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center text-muted-foreground">
          No providers have been invited for this RFQ yet.
        </CardContent>
      </Card>
    );
  }

  return (
    <DataTable
      columns={columns}
      data={invitedProviders}
      filterConfig={{
        search: {
          placeholder: "Search providers, status, notes...",
          // Enable global search across all filterable columns
          // This will search provider names, status, and bid notes
        },
      }}
      enableRowSelection={false}
      initialPageSize={10}
    />
  );
}

/**
 * Main component that uses initial data from server component
 * This prevents render-time state updates that cause React errors
 */
export function InvitedProvidersTable({
  rfqStatus,
  onAddBid,
  onUpdateBid,
  initialInvitedProviders = [],
}: Omit<InvitedProvidersTableProps, 'rfqId'>) {
  return (
    <InvitedProvidersTableContent
      rfqStatus={rfqStatus}
      onAddBid={onAddBid}
      onUpdateBid={onUpdateBid}
      initialInvitedProviders={initialInvitedProviders}
    />
  );
}
