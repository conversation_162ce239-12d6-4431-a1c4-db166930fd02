"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "@/components/ui/sonner";
import { useQuery } from "@tanstack/react-query";
import {
  CreateRFQSchema,
  CreateRFQInput,
  Country,
  CargoType,
  EquipmentType
} from "@/lib/schemas";
import { validatePostalCodeForCountry, getPostalCodeErrorMessage } from "@/lib/utils/postal-code-validation";

import { createRFQAction } from "@/lib/actions/rfq.actions";
import { getCountriesAction } from "@/lib/actions/country.actions";
import { getCargo } from "@/lib/actions/cargo";
import { getEquipment } from "@/lib/actions/equipment";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { DatePicker } from "@/components/ui/date-picker";
import { Combobox } from "@/components/ui/combobox";
import { AddressAutocomplete } from "@/components/ui/google-maps/address-autocomplete";
import { StructuredAddress } from "@/lib/services/google-maps/types";
import {
  Loader2,
  InfoIcon,
  MapPinIcon,
  TruckIcon,
  PackageIcon,
  CalendarIcon,
  WeightIcon,
  BoxesIcon,
  AlertCircle,
} from "lucide-react";
import { createLogger } from "@/lib/utils/logger/logger";
import { Skeleton } from "@/components/ui/skeleton";

import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { MultiSelect } from "@/components/ui/multi-select";
import { RFQConfirmationDialog } from "../dialogs/rfq-confirmation-dialog";

const logger = createLogger("RFQForm");

// Use CreateRFQInput as the form values type
type FormValues = CreateRFQInput;

// Define the form state interface
interface FormState {
  isSubmitting: boolean;
  originSearch: string;
  destinationSearch: string;
  validationErrors: Array<{field: string, message: string}>;
  showConfirmationDialog: boolean;
  formDataForConfirmation: FormValues | null;
}

// Define action types
type FormAction =
  | { type: 'SET_SUBMITTING'; payload: boolean }
  | { type: 'SET_ORIGIN_SEARCH'; payload: string }
  | { type: 'SET_DESTINATION_SEARCH'; payload: string }
  | { type: 'SET_VALIDATION_ERRORS'; payload: Array<{field: string, message: string}> }
  | { type: 'CLEAR_VALIDATION_ERRORS' }
  | { type: 'RESET_FORM_STATE' }
  | { type: 'SHOW_CONFIRMATION_DIALOG'; payload: FormValues }
  | { type: 'HIDE_CONFIRMATION_DIALOG' };

// Initial form state
const initialFormState: FormState = {
  isSubmitting: false,
  originSearch: '',
  destinationSearch: '',
  validationErrors: [],
  showConfirmationDialog: false,
  formDataForConfirmation: null,
};

// Form state reducer
function formReducer(state: FormState, action: FormAction): FormState {
  switch (action.type) {
    case 'SET_SUBMITTING':
      return { ...state, isSubmitting: action.payload };
    case 'SET_ORIGIN_SEARCH':
      return { ...state, originSearch: action.payload };
    case 'SET_DESTINATION_SEARCH':
      return { ...state, destinationSearch: action.payload };
    case 'SET_VALIDATION_ERRORS':
      return { ...state, validationErrors: action.payload };
    case 'CLEAR_VALIDATION_ERRORS':
      return { ...state, validationErrors: [] };
    case 'SHOW_CONFIRMATION_DIALOG':
      return {
        ...state,
        showConfirmationDialog: true,
        formDataForConfirmation: action.payload
      };
    case 'HIDE_CONFIRMATION_DIALOG':
      return {
        ...state,
        showConfirmationDialog: false
      };
    case 'RESET_FORM_STATE':
      return initialFormState;
    default:
      return state;
  }
}

// Map technical field names to user-friendly labels for error messages
const fieldToLabelMap: Record<string, string> = {
  // Basic Info
  expiration_date: "Expiration Date",
  notes: "Notes",

  // Shipping Details
  origin_country_id: "Origin Country",
  origin_city: "Origin City",
  origin_address: "Origin Address",
  origin_postal_code: "Origin Postal Code",
  destination_country_id: "Destination Country",
  destination_city: "Destination City",
  destination_address: "Destination Address",
  destination_postal_code: "Destination Postal Code",
  preferred_shipping_date: "Preferred Shipping Date",

  // Cargo Specifications
  cargo_type_id: "Cargo Type",
  cargo_type_ids: "Cargo Types",
  weight: "Weight",
  quantity: "Quantity",
  length: "Length",
  width: "Width",
  height: "Height",
  special_requirements: "Special Requirements",

  // Equipment Requirements
  equipment_type_id: "Equipment Type",
  equipment_quantity: "Equipment Quantity",
};

// Using types imported from schema files

// Custom hooks for data fetching using TanStack Query
function useCountries() {
  return useQuery<Country[]>({
    queryKey: ["countries"],
    queryFn: async () => {
      const result = await getCountriesAction();
      if (!result.success) {
        throw new Error(result.error || "Failed to fetch countries");
      }
      return result.data;
    },
  });
}

function useCargoTypes() {
  return useQuery<CargoType[]>({
    queryKey: ["cargo-types"],
    queryFn: async () => {
      const result = await getCargo();
      if (!result.data) {
        throw new Error(result.error || "Failed to fetch cargo types");
      }
      return result.data;
    },
  });
}

function useEquipmentTypes() {
  return useQuery<EquipmentType[]>({
    queryKey: ["equipment-types"],
    queryFn: async () => {
      const result = await getEquipment();
      if (!result.data) {
        throw new Error(result.error || "Failed to fetch equipment types");
      }
      return result.data;
    },
  });
}

// Helper function for toast notifications
const showToast = (
  type: "success" | "error" | "info",
  title: string,
  message: string
) => {
  const toastFn = type === "success" ? toast.success : type === "error" ? toast.error : toast.info;

  toastFn(
    <div className="space-y-1">
      <p className="font-semibold text-base">{title}</p>
      <p className="text-sm text-muted-foreground">{message}</p>
    </div>,
    {
      className: `p-4 rounded-lg border ${
        type === "success"
          ? "border-green-200 dark:border-green-900/30"
          : type === "error"
          ? "border-red-200 dark:border-red-900/30"
          : "border-blue-200 dark:border-blue-900/30"
      } bg-white dark:bg-card shadow-lg`,
      duration: 5000,
    }
  );
};

// RFQFormSkeleton component for loading state
function RFQFormSkeleton() {
  return (
    <div className="space-y-10 animate-pulse">
      {/* Header Skeleton */}
      <div className="flex items-center justify-between border-b border-border/40 pb-4">
        <Skeleton className="h-8 w-48" />
        <Skeleton className="h-8 w-24" />
      </div>

      {/* Basic Info Section Skeleton */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <Skeleton className="h-5 w-5 rounded-full" />
            <Skeleton className="h-6 w-40" />
          </div>
          <Skeleton className="h-4 w-64 mt-1" />
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-4 w-64" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-4 w-64" />
            </div>
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-32" />
            <Skeleton className="h-24 w-full" />
            <Skeleton className="h-4 w-64" />
          </div>
        </CardContent>
      </Card>

      {/* Shipping Details Section Skeleton */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <Skeleton className="h-5 w-5 rounded-full" />
            <Skeleton className="h-6 w-40" />
          </div>
          <Skeleton className="h-4 w-64 mt-1" />
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="space-y-5 rounded-lg border p-4">
              <div className="flex items-center gap-2">
                <Skeleton className="h-5 w-16" />
                <Skeleton className="h-5 w-32" />
              </div>
              <div className="space-y-5">
                {Array.from({ length: 4 }).map((_, i) => (
                  <div key={`origin-field-${i}`} className="space-y-2">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                ))}
              </div>
            </div>
            <div className="space-y-5 rounded-lg border p-4">
              <div className="flex items-center gap-2">
                <Skeleton className="h-5 w-16" />
                <Skeleton className="h-5 w-32" />
              </div>
              <div className="space-y-5">
                {Array.from({ length: 4 }).map((_, i) => (
                  <div key={`dest-field-${i}`} className="space-y-2">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Cargo Section Skeleton */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <Skeleton className="h-5 w-5 rounded-full" />
            <Skeleton className="h-6 w-40" />
          </div>
          <Skeleton className="h-4 w-64 mt-1" />
        </CardHeader>
        <CardContent className="space-y-6">
          <Skeleton className="h-16 w-full" />
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
            {Array.from({ length: 8 }).map((_, i) => (
              <Skeleton key={`cargo-type-${i}`} className="h-10 w-full" />
            ))}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {Array.from({ length: 2 }).map((_, i) => (
              <div key={`cargo-field-${i}`} className="space-y-2">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-4 w-48" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Equipment Section Skeleton */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <Skeleton className="h-5 w-5 rounded-full" />
            <Skeleton className="h-6 w-40" />
          </div>
          <Skeleton className="h-4 w-64 mt-1" />
        </CardHeader>
        <CardContent className="space-y-6">
          <Skeleton className="h-16 w-full" />
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
            {Array.from({ length: 4 }).map((_, i) => (
              <Skeleton key={`equipment-type-${i}`} className="h-10 w-full" />
            ))}
          </div>
          <div className="max-w-md space-y-2">
            <Skeleton className="h-4 w-32" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-4 w-48" />
          </div>
        </CardContent>
      </Card>

      {/* Submit Button Skeleton */}
      <div className="flex justify-end mt-8">
        <Skeleton className="h-9 w-24" />
      </div>
    </div>
  );
}

export function RFQForm() {
  const router = useRouter();

  // Use reducer for form state management
  const [formState, dispatch] = React.useReducer(formReducer, initialFormState);
  const {
    isSubmitting,
    originSearch,
    destinationSearch,
    validationErrors,
    showConfirmationDialog,
    formDataForConfirmation
  } = formState;

  // Fetch data using TanStack Query
  const {
    data: countries = [],
    isLoading: isLoadingCountries,
    error: countriesError
  } = useCountries();

  const {
    data: cargoTypes = [],
    isLoading: isLoadingCargoTypes,
    error: cargoTypesError
  } = useCargoTypes();

  const {
    data: equipmentTypes = [],
    isLoading: isLoadingEquipmentTypes,
    error: equipmentTypesError
  } = useEquipmentTypes();

  // Determine if data is still loading
  const isLoadingData = isLoadingCountries || isLoadingCargoTypes || isLoadingEquipmentTypes;

  // Show error toasts for data fetching errors
  React.useEffect(() => {
    if (countriesError) {
      showToast("error", "Error", "Failed to load countries data");
      logger.error("Failed to fetch countries:", countriesError);
    }
    if (cargoTypesError) {
      showToast("error", "Error", "Failed to load cargo types data");
      logger.error("Failed to fetch cargo types:", cargoTypesError);
    }
    if (equipmentTypesError) {
      showToast("error", "Error", "Failed to load equipment types data");
      logger.error("Failed to fetch equipment types:", equipmentTypesError);
    }
  }, [countriesError, cargoTypesError, equipmentTypesError]);

  // Calculate default expiration date (14 days from now)
  const getDefaultExpirationDate = () => {
    const date = new Date();
    date.setDate(date.getDate() + 14); // Add 14 days
    return date.toISOString();
  };

  // Initialize the form with default values using base schema
  const form = useForm<FormValues>({
    resolver: zodResolver(CreateRFQSchema),
    defaultValues: {
      status: "ready",
      notes: "",
      origin_city: "",
      origin_address: "",
      origin_postal_code: "",
      destination_city: "",
      destination_address: "",
      destination_postal_code: "",
      weight: undefined, // in tonnes - will be validated by schema
      quantity: 1,
      equipment_quantity: 1,
      cargo_type_ids: [], // Initialize empty array for multi-select
      expiration_date: getDefaultExpirationDate(), // Default to 14 days from now
    },
    // Only validate on submit
    mode: "onSubmit",
  });

  // Filter countries based on search
  const filteredOriginCountries = React.useMemo(() => {
    if (!originSearch.trim()) return countries;
    return countries.filter((country: Country) =>
      country.name.toLowerCase().includes(originSearch.toLowerCase()),
    );
  }, [countries, originSearch]);

  const filteredDestinationCountries = React.useMemo(() => {
    if (!destinationSearch.trim()) return countries;
    return countries.filter((country: Country) =>
      country.name.toLowerCase().includes(destinationSearch.toLowerCase()),
    );
  }, [countries, destinationSearch]);

  // Convert cargo types to options for MultiSelect
  const cargoTypeOptions = React.useMemo(() => {
    return cargoTypes.map((cargoType: CargoType) => ({
      value: cargoType.id,
      label: cargoType.name,
    }));
  }, [cargoTypes]);

  // Handle address selection
  const handleOriginAddressSelect = (address: StructuredAddress) => {
    form.setValue("origin_city", address.city || "");
    form.setValue("origin_postal_code", address.postal_code || "");
    form.setValue("origin_address", address.street_address || "");

    // Find country by name and set country_id
    const country = countries.find(
      (c: Country) => c.name.toLowerCase() === address.country?.toLowerCase(),
    );
    if (country) {
      form.setValue("origin_country_id", country.id);
    }
  };

  const handleDestinationAddressSelect = (address: StructuredAddress) => {
    form.setValue("destination_city", address.city || "");
    form.setValue("destination_postal_code", address.postal_code || "");
    form.setValue("destination_address", address.street_address || "");

    // Find country by name and set country_id
    const country = countries.find(
      (c: Country) => c.name.toLowerCase() === address.country?.toLowerCase(),
    );
    if (country) {
      form.setValue("destination_country_id", country.id);
    }
  };

  // Reset validation errors when form is reset
  React.useEffect(() => {
    const subscription = form.watch(() => {
      if (validationErrors.length > 0) {
        dispatch({ type: 'CLEAR_VALIDATION_ERRORS' });
      }
    });

    return () => subscription.unsubscribe();
  }, [form, validationErrors.length]);

  // Function to handle form validation errors
  const handleValidationErrors = (
    errors: Record<string, string[]> | { [key: string]: string[] | undefined },
  ) => {
    // Collect all errors for the summary
    const errorsList: {field: string, message: string}[] = [];

    // Display toast for each error
    Object.entries(errors).forEach(([field, messages]) => {
      if (messages && messages.length > 0) {
        // Get field label and error message
        const fieldLabel = fieldToLabelMap[field] || field;
        const errorMessage = messages[0] || `${fieldLabel} is required`;

        // Add to errors list
        errorsList.push({
          field: fieldLabel,
          message: errorMessage
        });

        // Show toast with the error message
        showToast("error", "Validation Error", errorMessage);
      }
    });

    // Update validation errors state
    dispatch({ type: 'SET_VALIDATION_ERRORS', payload: errorsList });

    // Scroll to top if there are errors
    if (errorsList.length > 0) {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  // Function to validate required fields
  const validateRequiredFields = (data: FormValues): boolean => {
    let isValid = true;

    // Check origin and destination countries
    if (!data.origin_country_id || !data.destination_country_id) {
      logger.error("Missing required country IDs:", {
        origin_country_id: data.origin_country_id,
        destination_country_id: data.destination_country_id,
      });

      if (!data.origin_country_id) {
        form.setError("origin_country_id", {
          type: "manual",
          message: "Origin country is required"
        });
      }

      if (!data.destination_country_id) {
        form.setError("destination_country_id", {
          type: "manual",
          message: "Destination country is required"
        });
      }

      isValid = false;
    }

    // Check cargo types
    if (!data.cargo_type_ids || data.cargo_type_ids.length === 0) {
      logger.error("Missing cargo type IDs");

      // Only set form error - let handleValidationErrors handle the rest
      form.setError("cargo_type_ids", {
        type: "manual",
        message: "Please select at least one cargo type"
      });

      isValid = false;
    }

    // Check equipment type
    if (!data.equipment_type_id) {
      logger.error("Missing equipment type ID");
      form.setError("equipment_type_id", {
        type: "manual",
        message: "Equipment type is required"
      });
      isValid = false;
    }

    // Check weight
    if (!data.weight) {
      logger.error("Missing weight");
      form.setError("weight", {
        type: "manual",
        message: "Weight is required"
      });
      isValid = false;
    }

    // Check origin and destination cities
    if (!data.origin_city || !data.destination_city) {
      logger.error("Missing city information:", {
        origin_city: data.origin_city,
        destination_city: data.destination_city,
      });

      if (!data.origin_city) {
        form.setError("origin_city", {
          type: "manual",
          message: "Origin city is required"
        });
      }

      if (!data.destination_city) {
        form.setError("destination_city", {
          type: "manual",
          message: "Destination city is required"
        });
      }

      isValid = false;
    }

    // Check origin and destination postal codes
    if (!data.origin_postal_code || !data.destination_postal_code) {
      logger.error("Missing postal code information:", {
        origin_postal_code: data.origin_postal_code,
        destination_postal_code: data.destination_postal_code,
      });

      if (!data.origin_postal_code) {
        form.setError("origin_postal_code", {
          type: "manual",
          message: "Origin postal code is required"
        });
      }

      if (!data.destination_postal_code) {
        form.setError("destination_postal_code", {
          type: "manual",
          message: "Destination postal code is required"
        });
      }

      isValid = false;
    }

    return isValid;
  };

  // Function to validate postal codes manually
  const validatePostalCodes = (data: FormValues): boolean => {
    let hasErrors = false;

    // Validate origin postal code
    if (data.origin_postal_code && data.origin_country_id) {
      const originCountry = countries.find(c => c.id === data.origin_country_id);
      if (originCountry) {
        const isValid = validatePostalCodeForCountry(data.origin_postal_code, originCountry.name);
        if (!isValid) {
          form.setError('origin_postal_code', {
            type: 'manual',
            message: getPostalCodeErrorMessage(originCountry.name)
          });
          hasErrors = true;
        }
      }
    }

    // Validate destination postal code
    if (data.destination_postal_code && data.destination_country_id) {
      const destinationCountry = countries.find(c => c.id === data.destination_country_id);
      if (destinationCountry) {
        const isValid = validatePostalCodeForCountry(data.destination_postal_code, destinationCountry.name);
        if (!isValid) {
          form.setError('destination_postal_code', {
            type: 'manual',
            message: getPostalCodeErrorMessage(destinationCountry.name)
          });
          hasErrors = true;
        }
      }
    }

    return !hasErrors;
  };

  // Function to handle initial form submission - shows confirmation dialog
  const onSubmit = async (data: FormValues) => {
    try {
      // Log form data for debugging
      logger.info(
        "Preparing RFQ form data for confirmation:",
        JSON.stringify(data, null, 2),
      );

      // Set status to ready
      data.status = "ready";

      // Validate required fields
      if (!validateRequiredFields(data)) {
        // Get form errors and handle them properly
        const formErrors = form.formState.errors;
        const errorMap: Record<string, string[]> = {};

        // Convert form errors to the format expected by handleValidationErrors
        Object.entries(formErrors).forEach(([field, error]) => {
          if (error?.message) {
            errorMap[field] = [error.message];
          }
        });

        // Handle validation errors (this will show toasts and update state)
        if (Object.keys(errorMap).length > 0) {
          handleValidationErrors(errorMap);

          // Special handling for cargo types - scroll to the section
          if (errorMap.cargo_type_ids) {
            setTimeout(() => {
              const cargoSection = document.querySelector('[name="cargo_type_ids"]');
              if (cargoSection) {
                cargoSection.scrollIntoView({ behavior: 'smooth', block: 'center' });
              }
            }, 100);
          }
        }

        return;
      }

      // Validate postal codes if countries are available
      if (countries.length > 0 && !validatePostalCodes(data)) {
        showToast("error", "Validation Error", "Please check the postal codes and try again");
        return;
      }

      // Show confirmation dialog with form data
      dispatch({ type: 'SHOW_CONFIRMATION_DIALOG', payload: data });

    } catch (error) {
      logger.error("Error preparing form for confirmation:", error);
      showToast("error", "Error", "An unexpected error occurred");
    }
  };

  // Function to handle final form submission after confirmation
  const handleFinalSubmit = async () => {
    try {
      if (!formDataForConfirmation) {
        showToast("error", "Error", "No form data available for submission");
        return;
      }

      dispatch({ type: 'SET_SUBMITTING', payload: true });

      // Submit the form
      logger.info("Calling createRFQAction with confirmed data");
      const result = await createRFQAction(formDataForConfirmation);

      if (result.success) {
        // Clear validation errors and hide confirmation dialog
        dispatch({ type: 'CLEAR_VALIDATION_ERRORS' });
        dispatch({ type: 'HIDE_CONFIRMATION_DIALOG' });

        showToast("success", "Success", "RFQ created successfully");
        router.push(`/dashboard/rfqs/${result.data.id}`);
      } else {
        // Handle validation errors if they exist
        if (result.fieldErrors) {
          handleValidationErrors(result.fieldErrors);
        } else {
          // General error message
          showToast("error", "Error", result.error || "Failed to create RFQ");
        }
      }
    } catch (error) {
      logger.error("Error submitting form:", error);
      showToast("error", "Error", "An unexpected error occurred");
    } finally {
      dispatch({ type: 'SET_SUBMITTING', payload: false });
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="w-full space-y-12">
        {/* Confirmation Dialog */}
        <RFQConfirmationDialog
          open={showConfirmationDialog}
          onOpenChange={(open) => {
            if (!open) dispatch({ type: 'HIDE_CONFIRMATION_DIALOG' });
          }}
          formData={formDataForConfirmation || form.getValues()}
          onConfirm={handleFinalSubmit}
          isSubmitting={isSubmitting}
          countries={countries}
          cargoTypes={cargoTypes}
          equipmentTypes={equipmentTypes}
        />

        {isLoadingData && <RFQFormSkeleton />}

        {!isLoadingData && (
          <>
            {/* Validation Errors Summary */}
            {validationErrors.length > 0 && (
              <Alert variant="destructive" className="shadow-sm mb-6">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  <div className="space-y-2">
                    <h3 className="font-medium">Please fix the following errors:</h3>
                    <ul className="list-disc pl-5 space-y-1.5">
                      {validationErrors.map((error, index) => (
                        <li key={index} className="text-sm">
                          <span className="font-medium">{error.field}:</span> {error.message}
                        </li>
                      ))}
                    </ul>
                  </div>
                </AlertDescription>
              </Alert>
            )}

            {/* Basic Information Section */}
            <div className="bg-card rounded-lg border border-border/60 shadow-sm overflow-hidden">
              <div className="border-b border-border/40 bg-muted/30 px-6 py-3">
                <div className="flex items-center gap-2">
                  <div className="bg-primary/10 p-1 rounded-full">
                    <InfoIcon className="h-4 w-4 text-primary" />
                  </div>
                  <h2 className="text-lg font-medium">Basic Information</h2>
                </div>
              </div>
              <div className="p-6 space-y-6">
                <Alert className="mb-4 bg-blue-50/50 dark:bg-blue-950/20 text-blue-800 dark:text-blue-200 border-blue-200 dark:border-blue-800 py-3 text-xs">
                  <InfoIcon className="h-3.5 w-3.5" />
                  <AlertDescription>
                    Enter the basic information for your Request for Quote.
                  </AlertDescription>
                </Alert>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="expiration_date"
                    render={({ field }) => (
                      <FormItem className="space-y-1.5">
                        <FormLabel className="text-sm">
                          <span className="flex items-center gap-1.5">
                            <div className="bg-primary/10 p-0.5 rounded-full">
                              <CalendarIcon className="h-3 w-3 text-primary" />
                            </div>
                            Expiration Date
                            <span className="text-destructive">*</span>
                          </span>
                        </FormLabel>
                        <FormControl>
                          <DatePicker
                            date={field.value ? new Date(field.value) : undefined}
                            setDate={(date) => {
                              field.onChange(date ? date.toISOString() : undefined);
                            }}
                            placeholder="Select expiration date"
                            error={form.formState.errors.expiration_date?.message}
                            className="transition-all duration-200"
                          />
                        </FormControl>
                        <FormDescription className="text-sm">
                          The date after which this RFQ is no longer valid.
                        </FormDescription>
                        <FormMessage className="text-sm" />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="preferred_shipping_date"
                    render={({ field }) => (
                      <FormItem className="space-y-1.5">
                        <FormLabel className="text-sm">
                          <span className="flex items-center gap-1.5">
                            <div className="bg-primary/10 p-0.5 rounded-full">
                              <TruckIcon className="h-3 w-3 text-primary" />
                            </div>
                            Preferred Shipping Date
                          </span>
                        </FormLabel>
                        <FormControl>
                          <DatePicker
                            date={field.value ? new Date(field.value) : undefined}
                            setDate={(date) => {
                              field.onChange(date ? date.toISOString() : undefined);
                            }}
                            placeholder="Select preferred shipping date"
                            error={
                              form.formState.errors.preferred_shipping_date?.message
                            }
                            className="transition-all duration-200"
                          />
                        </FormControl>
                        <FormDescription className="text-sm">
                          When would you like your cargo to be shipped?
                        </FormDescription>
                        <FormMessage className="text-sm" />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem className="space-y-1.5">
                      <FormLabel className="text-sm">
                        <span className="flex items-center gap-1.5">
                          <div className="bg-primary/10 p-0.5 rounded-full">
                            <InfoIcon className="h-3 w-3 text-primary" />
                          </div>
                          Notes
                        </span>
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter any additional notes or requirements"
                          className="min-h-[120px] resize-y transition-all duration-200 border-border/60 focus-visible:ring-primary/30"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription className="text-sm">
                        Any additional information or special instructions for this RFQ.
                      </FormDescription>
                      <FormMessage className="text-sm" />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </>
        )}

        {!isLoadingData && (
          /* Shipping Details Section */
          <div className="bg-card rounded-lg border border-border/60 shadow-sm overflow-visible">
            <div className="border-b border-border/40 bg-muted/30 px-6 py-3">
              <div className="flex items-center gap-2">
                <div className="bg-primary/10 p-1 rounded-full">
                  <MapPinIcon className="h-4 w-4 text-primary" />
                </div>
                <h2 className="text-lg font-medium">Shipping Details</h2>
              </div>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Origin Information */}
                <div className="border border-green-100 dark:border-green-800 rounded-md p-5 bg-green-50/10 dark:bg-green-950/10">
                  <div className="flex items-center gap-1.5 mb-3">
                    <Badge variant="outline" className="bg-green-50 dark:bg-green-950/20 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800 rounded-full px-2 py-0 text-xs">Origin</Badge>
                    <h3 className="text-sm font-medium">Pickup Location</h3>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="origin_country_id"
                      render={({ field }) => (
                        <FormItem className="space-y-1.5">
                          <FormLabel required className="text-sm">Country</FormLabel>
                          <FormControl>
                            <Combobox
                              items={filteredOriginCountries}
                              value={
                                field.value
                                  ? countries.find((c: Country) => c.id === field.value)
                                      ?.name || ""
                                  : ""
                              }
                              onValueChange={(value) => {
                                dispatch({ type: 'SET_ORIGIN_SEARCH', payload: value });
                              }}
                              onSelect={(country: Country) => {
                                field.onChange(country.id);
                                dispatch({ type: 'SET_ORIGIN_SEARCH', payload: "" });
                              }}
                              placeholder="Select a country"
                              loading={isLoadingData}
                              disabled={isLoadingData}
                              error={form.formState.errors.origin_country_id?.message}
                              getValue={(country: Country) => country.id}
                              getDisplayValue={(country: Country) => country.name}
                            />
                          </FormControl>
                          <FormMessage className="text-xs" />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="origin_city"
                      render={({ field }) => (
                        <FormItem className="space-y-1.5">
                          <FormLabel required className="text-sm">City</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter city"
                              {...field}
                              value={field.value || ""}
                              className="h-10"
                            />
                          </FormControl>
                          <FormMessage className="text-sm" />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4 mt-4">
                    <FormField
                      control={form.control}
                      name="origin_postal_code"
                      render={({ field }) => (
                        <FormItem className="space-y-1.5">
                          <FormLabel required className="text-sm">Postal Code</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter postal code"
                              {...field}
                              value={field.value || ""}
                              className="h-9"
                              onBlur={(e) => {
                                field.onBlur();
                                // Validate postal code on blur if country is selected
                                const originCountryId = form.getValues('origin_country_id');
                                if (e.target.value && originCountryId && countries.length > 0) {
                                  const originCountry = countries.find(c => c.id === originCountryId);
                                  if (originCountry) {
                                    const isValid = validatePostalCodeForCountry(e.target.value, originCountry.name);
                                    if (!isValid) {
                                      form.setError('origin_postal_code', {
                                        type: 'manual',
                                        message: getPostalCodeErrorMessage(originCountry.name)
                                      });
                                    } else {
                                      form.clearErrors('origin_postal_code');
                                    }
                                  }
                                }
                              }}
                            />
                          </FormControl>
                          <FormMessage className="text-xs" />
                        </FormItem>
                      )}
                    />

                    <div className="relative z-30">
                      <FormField
                        control={form.control}
                        name="origin_address"
                        render={({ field }) => (
                          <FormItem className="space-y-1.5">
                            <FormLabel className="text-xs">Address</FormLabel>
                            <FormControl>
                              <AddressAutocomplete
                                value={field.value || ""}
                                onChange={field.onChange}
                                onSelect={handleOriginAddressSelect}
                                placeholder="Enter address"
                              />
                            </FormControl>
                            <FormMessage className="text-xs" />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                </div>

                {/* Destination Information */}
                <div className="border border-blue-100 dark:border-blue-800 rounded-md p-5 bg-blue-50/10 dark:bg-blue-950/10">
                  <div className="flex items-center gap-1.5 mb-3">
                    <Badge variant="outline" className="bg-blue-50 dark:bg-blue-950/20 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-800 rounded-full px-2 py-0 text-xs">Destination</Badge>
                    <h3 className="text-sm font-medium">Delivery Location</h3>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="destination_country_id"
                      render={({ field }) => (
                        <FormItem className="space-y-1.5">
                          <FormLabel required className="text-xs">Country</FormLabel>
                          <FormControl>
                            <Combobox
                              items={filteredDestinationCountries}
                              value={
                                field.value
                                  ? countries.find((c: Country) => c.id === field.value)
                                      ?.name || ""
                                  : ""
                              }
                              onValueChange={(value) => {
                                dispatch({ type: 'SET_DESTINATION_SEARCH', payload: value });
                              }}
                              onSelect={(country: Country) => {
                                field.onChange(country.id);
                                dispatch({ type: 'SET_DESTINATION_SEARCH', payload: "" });
                              }}
                              placeholder="Select a country"
                              loading={isLoadingData}
                              disabled={isLoadingData}
                              error={
                                form.formState.errors.destination_country_id?.message
                              }
                              getValue={(country: Country) => country.id}
                              getDisplayValue={(country: Country) => country.name}
                            />
                          </FormControl>
                          <FormMessage className="text-xs" />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="destination_city"
                      render={({ field }) => (
                        <FormItem className="space-y-1.5">
                          <FormLabel required className="text-xs">City</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter city"
                              {...field}
                              value={field.value || ""}
                              className="h-9"
                            />
                          </FormControl>
                          <FormMessage className="text-xs" />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4 mt-4">
                    <FormField
                      control={form.control}
                      name="destination_postal_code"
                      render={({ field }) => (
                        <FormItem className="space-y-1.5">
                          <FormLabel required className="text-xs">Postal Code</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter postal code"
                              {...field}
                              value={field.value || ""}
                              className="h-9"
                              onBlur={(e) => {
                                field.onBlur();
                                // Validate postal code on blur if country is selected
                                const destinationCountryId = form.getValues('destination_country_id');
                                if (e.target.value && destinationCountryId && countries.length > 0) {
                                  const destinationCountry = countries.find(c => c.id === destinationCountryId);
                                  if (destinationCountry) {
                                    const isValid = validatePostalCodeForCountry(e.target.value, destinationCountry.name);
                                    if (!isValid) {
                                      form.setError('destination_postal_code', {
                                        type: 'manual',
                                        message: getPostalCodeErrorMessage(destinationCountry.name)
                                      });
                                    } else {
                                      form.clearErrors('destination_postal_code');
                                    }
                                  }
                                }
                              }}
                            />
                          </FormControl>
                          <FormMessage className="text-xs" />
                        </FormItem>
                      )}
                    />

                    <div className="relative z-20">
                      <FormField
                        control={form.control}
                        name="destination_address"
                        render={({ field }) => (
                          <FormItem className="space-y-1.5">
                            <FormLabel className="text-xs">Address</FormLabel>
                            <FormControl>
                              <AddressAutocomplete
                                value={field.value || ""}
                                onChange={field.onChange}
                                onSelect={handleDestinationAddressSelect}
                                placeholder="Enter address"
                              />
                            </FormControl>
                            <FormMessage className="text-xs" />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {!isLoadingData && (
          /* Cargo Specifications Section */
          <div className="bg-card rounded-lg border border-border/60 shadow-sm overflow-hidden">
            <div className="border-b border-border/40 bg-muted/30 px-6 py-3">
              <div className="flex items-center gap-2">
                <div className="bg-primary/10 p-1 rounded-full">
                  <PackageIcon className="h-4 w-4 text-primary" />
                </div>
                <h2 className="text-lg font-medium">Cargo Specifications</h2>
              </div>
            </div>
            <div className="p-6 space-y-6">
              {/* Cargo Types Selection */}
              <div>
                <Alert className="mb-4 bg-blue-50/50 dark:bg-blue-950/20 text-blue-800 dark:text-blue-200 border-blue-200 dark:border-blue-800 py-3 text-xs">
                  <InfoIcon className="h-3.5 w-3.5" />
                  <AlertDescription>
                    Select all cargo types that apply to your shipment. You must select at least one cargo type.
                  </AlertDescription>
                </Alert>

                <FormField
                  control={form.control}
                  name="cargo_type_ids"
                  render={({ field }) => (
                    <FormItem className="space-y-2">
                      <FormLabel required className="text-sm">
                        <span className="flex items-center gap-1.5">
                          <div className="bg-primary/10 p-0.5 rounded-full">
                            <BoxesIcon className="h-3 w-3 text-primary" />
                          </div>
                          Cargo Types
                        </span>
                      </FormLabel>
                      <FormControl>
                        <MultiSelect
                          options={cargoTypeOptions}
                          selected={field.value || []}
                          onChange={field.onChange}
                          placeholder="Search and select cargo types..."
                          disabled={isLoadingData}
                          error={form.formState.errors.cargo_type_ids?.message}
                          className="min-h-[38px]"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Weight, Quantity and Dimensions in one row */}
              <div className="grid grid-cols-2 md:grid-cols-5 gap-5">
                <FormField
                  control={form.control}
                  name="weight"
                  render={({ field }) => (
                    <FormItem className="space-y-1.5 md:col-span-1">
                      <FormLabel required className="text-sm">
                        <span className="flex items-center gap-1.5">
                          <div className="bg-primary/10 p-0.5 rounded-full">
                            <WeightIcon className="h-3 w-3 text-primary" />
                          </div>
                          Weight (tonnes)
                        </span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="Weight"
                          {...field}
                          value={field.value || ""}
                          onChange={(e) => {
                            const value = e.target.value;
                            field.onChange(value === "" ? undefined : Number(value));
                          }}
                          className="h-10"
                        />
                      </FormControl>
                      <FormMessage className="text-sm" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="quantity"
                  render={({ field }) => (
                    <FormItem className="space-y-1.5 md:col-span-1">
                      <FormLabel className="text-sm">
                        <span className="flex items-center gap-1.5">
                          <div className="bg-primary/10 p-0.5 rounded-full">
                            <BoxesIcon className="h-3 w-3 text-primary" />
                          </div>
                          Quantity
                        </span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="Quantity"
                          {...field}
                          value={field.value || ""}
                          onChange={(e) => {
                            const value = e.target.value;
                            field.onChange(value === "" ? undefined : Number(value));
                          }}
                          className="h-10"
                        />
                      </FormControl>
                      <FormMessage className="text-sm" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="length"
                  render={({ field }) => (
                    <FormItem className="space-y-1.5 md:col-span-1">
                      <FormLabel className="text-sm">Length (m)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="Length"
                          {...field}
                          value={field.value || ""}
                          onChange={(e) => {
                            const value = e.target.value;
                            field.onChange(value === "" ? undefined : Number(value));
                          }}
                          className="h-10"
                        />
                      </FormControl>
                      <FormMessage className="text-sm" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="width"
                  render={({ field }) => (
                    <FormItem className="space-y-1.5 md:col-span-1">
                      <FormLabel className="text-sm">Width (m)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="Width"
                          {...field}
                          value={field.value || ""}
                          onChange={(e) => {
                            const value = e.target.value;
                            field.onChange(value === "" ? undefined : Number(value));
                          }}
                          className="h-10"
                        />
                      </FormControl>
                      <FormMessage className="text-sm" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="height"
                  render={({ field }) => (
                    <FormItem className="space-y-1.5 md:col-span-1">
                      <FormLabel className="text-sm">Height (m)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="Height"
                          {...field}
                          value={field.value || ""}
                          onChange={(e) => {
                            const value = e.target.value;
                            field.onChange(value === "" ? undefined : Number(value));
                          }}
                          className="h-10"
                        />
                      </FormControl>
                      <FormMessage className="text-sm" />
                    </FormItem>
                  )}
                />
              </div>

              {/* Special Requirements */}
              <FormField
                control={form.control}
                name="special_requirements"
                render={({ field }) => (
                  <FormItem className="space-y-1.5">
                    <FormLabel className="text-sm">
                      <span className="flex items-center gap-1.5">
                        <div className="bg-primary/10 p-0.5 rounded-full">
                          <InfoIcon className="h-3 w-3 text-primary" />
                        </div>
                        Special Requirements
                      </span>
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter any special requirements or handling instructions"
                        className="min-h-[80px] resize-y"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
        )}

        {!isLoadingData && (
          /* Equipment Requirements Section */
          <div className="bg-card rounded-lg border border-border/60 shadow-sm overflow-hidden">
            <div className="border-b border-border/40 bg-muted/30 px-6 py-3">
              <div className="flex items-center gap-2">
                <div className="bg-primary/10 p-1 rounded-full">
                  <TruckIcon className="h-4 w-4 text-primary" />
                </div>
                <h2 className="text-lg font-medium">Equipment Requirements</h2>
              </div>
            </div>
            <div className="p-6 space-y-6">
              <Alert className="mb-4 bg-blue-50/50 dark:bg-blue-950/20 text-blue-800 dark:text-blue-200 border-blue-200 dark:border-blue-800 py-3 text-sm">
                <InfoIcon className="h-4.5 w-4.5" />
                <AlertDescription>
                  Select the type of equipment needed for your shipment. This is required to process your RFQ.
                </AlertDescription>
              </Alert>

              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="md:col-span-3">
                  <FormField
                    control={form.control}
                    name="equipment_type_id"
                    render={({ field }) => (
                      <FormItem className="space-y-3">
                        <FormLabel required className="text-sm">
                          <span className="flex items-center gap-1.5">
                            <div className="bg-primary/10 p-0.5 rounded-full">
                              <TruckIcon className="h-3 w-3 text-primary" />
                            </div>
                            Equipment Type
                          </span>
                        </FormLabel>
                        <FormControl>
                          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
                            {equipmentTypes.map((equipmentType) => (
                              <div
                                key={equipmentType.id}
                                className={`border rounded-md p-4 cursor-pointer transition-all duration-200 ${
                                  field.value === equipmentType.id
                                    ? "bg-primary/10 border-primary shadow-sm"
                                    : "hover:bg-muted hover:border-muted-foreground/20"
                                }`}
                                onClick={() => field.onChange(equipmentType.id)}
                              >
                                <div className="flex items-center space-x-2">
                                  <div
                                    className={`w-4 h-4 rounded-full border transition-all duration-200 ${
                                      field.value === equipmentType.id
                                        ? "bg-primary border-primary"
                                        : "border-muted-foreground"
                                    }`}
                                  />
                                  <span className="text-sm font-medium">{equipmentType.name}</span>
                                </div>
                              </div>
                            ))}
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div>
                  <FormField
                    control={form.control}
                    name="equipment_quantity"
                    render={({ field }) => (
                      <FormItem className="space-y-1.5">
                        <FormLabel className="text-sm">
                          <span className="flex items-center gap-1.5">
                            <div className="bg-primary/10 p-0.5 rounded-full">
                              <BoxesIcon className="h-3 w-3 text-primary" />
                            </div>
                            Equipment Quantity
                          </span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="Enter quantity"
                            {...field}
                            value={field.value || ""}
                            onChange={(e) => {
                              const value = e.target.value;
                              field.onChange(value === "" ? undefined : Number(value));
                            }}
                            className="h-10"
                          />
                        </FormControl>
                        <FormMessage className="text-sm" />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </div>
          </div>
        )}



        {/* Submit Button */}
        {!isLoadingData && (
          <div className="flex justify-end mt-10">
            <Button
              type="submit"
              size="lg"
              disabled={isSubmitting}
              className="transition-all duration-200"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  <span>Processing...</span>
                </>
              ) : (
                <span>Review & Submit RFQ</span>
              )}
            </Button>
          </div>
        )}
      </form>
    </Form>
  );
}