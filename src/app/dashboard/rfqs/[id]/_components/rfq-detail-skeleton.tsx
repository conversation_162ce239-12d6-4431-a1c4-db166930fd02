import { Skeleton } from "@/components/ui/skeleton";

/**
 * Loading skeleton for the RFQ detail page
 *
 * This component displays a skeleton UI while the RFQ detail data is being loaded.
 */
export function RFQDetailSkeleton() {
  return (
    <div className="space-y-6">
      {/* Back button skeleton */}
      <div className="mb-6">
        <Skeleton className="h-10 w-32" />
      </div>

      {/* Header section skeleton */}
      <div className="mb-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
          <div>
            <Skeleton className="h-8 w-64 mb-2" />
            <Skeleton className="h-4 w-96" />
          </div>
          <Skeleton className="h-10 w-32" />
        </div>

        {/* Status timeline skeleton */}
        <div className="mb-8 mt-8">
          <div className="flex items-center w-full">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="flex items-center flex-1">
                <div className="relative flex flex-col items-center">
                  <Skeleton className="h-8 w-8 rounded-full" />
                  <Skeleton className="h-4 w-16 mt-10" />
                </div>
                {i < 3 && <Skeleton className="h-[2px] flex-1" />}
              </div>
            ))}
          </div>
        </div>

        {/* Summary panel skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 bg-muted/30 rounded-lg mb-8">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="flex items-center gap-3">
              <Skeleton className="h-10 w-10 rounded-full" />
              <div>
                <Skeleton className="h-4 w-16 mb-1" />
                <Skeleton className="h-5 w-24" />
              </div>
            </div>
          ))}
        </div>
      </div>



      {/* Special requirements skeleton - simplified */}
      <div className="mb-4">
        <Skeleton className="h-4 w-32 mb-2" />
        <Skeleton className="h-16 w-full" />
      </div>

      {/* Notes skeleton */}
      <div className="bg-background border rounded-lg shadow-sm overflow-hidden mb-6">
        <div className="flex items-center gap-3 px-6 py-4 bg-muted/20">
          <Skeleton className="h-8 w-8 rounded-full" />
          <Skeleton className="h-6 w-16" />
        </div>
        <div className="px-6 py-5">
          <Skeleton className="h-20 w-full" />
        </div>
      </div>

      {/* Tab navigation skeleton */}
      <div>
        <div className="flex mb-6 gap-2">
          <Skeleton className="h-11 w-32" />
          <Skeleton className="h-11 w-32" />
          <Skeleton className="h-11 w-36" />
        </div>

        {/* Tab content skeleton */}
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="flex items-center justify-between p-4 border rounded">
              <div className="flex items-center gap-3">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div>
                  <Skeleton className="h-4 w-32 mb-1" />
                  <Skeleton className="h-3 w-24" />
                </div>
              </div>
              <Skeleton className="h-8 w-20" />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
