import Link from "next/link";
import { notFound } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Edit,
  UsersIcon,
  Mail,
  Route
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { getRFQAction, getRFQProvidersAction, getRFQBidsAction, mergeProvidersWithBidsAction, getInvitedProvidersAction } from "@/lib/actions/rfq.actions";
import { format } from "date-fns";

import { RFQSummaryPanel } from "../../_components/rfq-summary-panel";
import { ProviderSelection } from "../../_components/provider-selection";
import { EmailHistory } from "../../_components/email-history";
import { LazyRouteMap } from "./route-map";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("RFQDetailContent");

// Type interface for cargo type relations
interface CargoTypeRelation {
  name?: string;
}



/**
 * Server Component for fetching and displaying RFQ details
 *
 * This component is responsible for:
 * - Fetching RFQ data using the getRFQAction
 * - Handling error states
 * - Displaying RFQ details and tabs
 */
export async function RFQDetailContent({ id }: { id: string }) {
  logger.info("Fetching RFQ details", { id });

  const result = await getRFQAction(id);

  if (!result.success || !result.data) {
    logger.error("Error fetching RFQ details:", result.success ? "No data returned" : result.error);
    notFound();
  }

  const rfq = result.data;

  // Fetch invited providers data in the server component
  let invitedProviders: any[] = [];
  try {
    // Fetch RFQ providers and bids in parallel
    const [providersResponse, bidsResponse] = await Promise.all([
      getRFQProvidersAction(rfq.id),
      getRFQBidsAction(rfq.id)
    ]);

    if (providersResponse.success && bidsResponse.success) {
      // Merge providers with bids
      const mergeResult = await mergeProvidersWithBidsAction(
        providersResponse.data || [],
        bidsResponse.data || []
      );

      if (mergeResult.success) {
        // Filter to get only invited providers
        const invitedResult = await getInvitedProvidersAction(mergeResult.data);
        if (invitedResult.success) {
          invitedProviders = invitedResult.data;
        }
      }
    }
  } catch (error) {
    logger.error("Error fetching invited providers in server component:", error);
    // Continue with empty array - the client component will handle the error state
  }

  // Format the status badge
  let statusVariant: "default" | "secondary" | "outline" | "destructive" =
    "default";
  switch (rfq.status) {
    case "draft":
      statusVariant = "outline";
      break;
    case "ready":
      statusVariant = "secondary";
      break;
    case "sent":
      statusVariant = "default";
      break;
    case "closed":
      statusVariant = "destructive";
      break;
  }

  // Format dates
  const createdAt = rfq.created_at
    ? format(new Date(rfq.created_at), "PPP")
    : "Unknown";
  const expirationDate = rfq.expiration_date
    ? format(new Date(rfq.expiration_date), "PPP")
    : "Not set";
  const preferredShippingDate = rfq.preferred_shipping_date
    ? format(new Date(rfq.preferred_shipping_date), "PPP")
    : "Not specified";

  // Format cargo types for display
  const cargoTypesDisplay = rfq.cargo_types && rfq.cargo_types.length > 0
    ? rfq.cargo_types
        .map((ct: CargoTypeRelation) => ct.name)
        .filter(Boolean)
        .join(", ")
    : undefined;

  // Format dimensions for display
  const dimensionsDisplay = rfq.length && rfq.width && rfq.height
    ? `${rfq.length} × ${rfq.width} × ${rfq.height} m`
    : undefined;

  return (
    <>
      {/* Enhanced Header Section with Integrated Navigation */}
      <div className="mb-8">
        {/* Navigation and Title Row */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
          <div className="flex flex-col gap-3">
            {/* Main Title with Sequence Number */}
            <div>
              <h1 className="text-3xl font-bold tracking-tight text-foreground mb-2">
                {rfq.sequence_number || `RFQ-${rfq.id.slice(0, 8)}`}
              </h1>
              {rfq.title && (
                <p className="text-lg text-muted-foreground mb-3">
                  {rfq.title}
                </p>
              )}
              <div className="flex items-center gap-3">
                <span className="text-sm text-muted-foreground">
                  Created on {createdAt}
                </span>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 self-end md:self-auto">
            <Button size="sm" asChild>
              <Link href={`/dashboard/rfqs/${rfq.id}/edit`}>
                <Edit className="mr-1 h-3.5 w-3.5" />
                Edit RFQ
              </Link>
            </Button>
          </div>
        </div>

        {/* Enhanced Summary Panel with Route Information */}
        <RFQSummaryPanel
          weight={rfq.weight}
          quantity={rfq.quantity}
          equipmentType={rfq.equipment_type_name || "Unknown Equipment"}
          equipmentQuantity={rfq.equipment_quantity}
          preferredShippingDate={
            preferredShippingDate !== "Not specified"
              ? preferredShippingDate
              : undefined
          }
          cargoTypes={cargoTypesDisplay}
          dimensions={dimensionsDisplay}
          status={rfq.status}
          statusVariant={statusVariant}
          expirationDate={expirationDate !== "Not set" ? expirationDate : undefined}
          originCity={rfq.origin_city}
          originAddress={rfq.origin_address}
          originPostalCode={rfq.origin_postal_code}
          originCountryName={rfq.countries_origin?.name}
          originCountryCode={rfq.countries_origin?.alpha2_code}
          destinationCity={rfq.destination_city}
          destinationAddress={rfq.destination_address}
          destinationPostalCode={rfq.destination_postal_code}
          destinationCountryName={rfq.countries_destination?.name}
          destinationCountryCode={rfq.countries_destination?.alpha2_code}
          className="mb-6"
        />

        {/* Additional Information - Compact Display */}
        {(rfq.special_requirements || rfq.notes) && (
          <div className="mb-6 space-y-3">
            {rfq.special_requirements && (
              <div className="text-sm">
                <span className="font-medium text-muted-foreground">Special Requirements:</span>{" "}
                <span className="text-foreground whitespace-pre-wrap">
                  {rfq.special_requirements}
                </span>
              </div>
            )}
            {rfq.notes && (
              <div className="text-sm">
                <span className="font-medium text-muted-foreground">Notes:</span>{" "}
                <span className="text-foreground whitespace-pre-wrap">
                  {rfq.notes}
                </span>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Tab Navigation */}
      <Tabs defaultValue="providers" className="w-full">
        <TabsList className="mb-6 grid w-full grid-cols-3 h-11">
          <TabsTrigger
            value="providers"
            className="min-w-[120px] px-6 py-2.5 text-sm font-medium data-[state=active]:bg-background data-[state=active]:shadow-sm"
          >
            <UsersIcon className="mr-2 h-4 w-4" />
            Providers
          </TabsTrigger>
          <TabsTrigger
            value="route-map"
            className="min-w-[120px] px-6 py-2.5 text-sm font-medium data-[state=active]:bg-background data-[state=active]:shadow-sm"
          >
            <Route className="mr-2 h-4 w-4" />
            Route Map
          </TabsTrigger>
          <TabsTrigger
            value="email-history"
            className="min-w-[120px] px-6 py-2.5 text-sm font-medium data-[state=active]:bg-background data-[state=active]:shadow-sm"
          >
            <Mail className="mr-2 h-4 w-4" />
            Email History
          </TabsTrigger>
        </TabsList>

        <TabsContent value="providers">
          <ProviderSelection
            rfqId={rfq.id}
            rfqStatus={rfq.status}
            initialInvitedProviders={invitedProviders}
          />
        </TabsContent>

        <TabsContent value="route-map">
          <LazyRouteMap
            rfqId={rfq.id}
            originLat={rfq.origin_lat}
            originLng={rfq.origin_lng}
            destinationLat={rfq.destination_lat}
            destinationLng={rfq.destination_lng}
            routeDistanceKm={rfq.route_distance_km}
            coordinatesResolvedAt={rfq.coordinates_resolved_at}
            routeDistanceCalculatedAt={rfq.route_distance_calculated_at}
            originCity={rfq.origin_city}
            originAddress={rfq.origin_address}
            originPostalCode={rfq.origin_postal_code || undefined}
            originCountryName={rfq.countries_origin?.name}
            destinationCity={rfq.destination_city}
            destinationAddress={rfq.destination_address}
            destinationPostalCode={rfq.destination_postal_code || undefined}
            destinationCountryName={rfq.countries_destination?.name}
          />
        </TabsContent>

        <TabsContent value="email-history">
          <EmailHistory rfqId={rfq.id} />
        </TabsContent>
      </Tabs>
    </>
  );
}
