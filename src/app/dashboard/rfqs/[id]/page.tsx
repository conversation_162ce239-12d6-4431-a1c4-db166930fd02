import { Metadata } from "next";
import { Suspense } from "react";
import { RFQDetailContent } from "./_components/rfq-detail-content";
import { RFQDetailSkeleton } from "./_components/rfq-detail-skeleton";
import { getRFQAction } from "@/lib/actions/rfq.actions";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ id: string }>;
}): Promise<Metadata> {
  const { id } = await params;

  try {
    const result = await getRFQAction(id);
    const rfq = result.success ? result.data : null;
    const sequenceNumber = rfq?.sequence_number || `RFQ-${id.slice(0, 8)}`;

    return {
      title: `${sequenceNumber} | Steelflow`,
      description: rfq?.title
        ? `RFQ Details: ${rfq.title}`
        : "View Request for Quote details",
    };
  } catch (error) {
    return {
      title: "RFQ Details | Steelflow",
      description: "View Request for Quote details",
    };
  }
}

export default async function RFQDetailPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  // Await params before accessing its properties
  const { id } = await params;

  return (
    <div className="container mx-auto py-2 px-0">
      <Suspense fallback={<RFQDetailSkeleton />}>
        <RFQDetailContent id={id} />
      </Suspense>
    </div>
  );
}
