"use client";

import { useState, useMemo, useCallback } from "react";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  RowSelectionState,
} from "@tanstack/react-table";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { toast } from "@/components/ui/sonner";
import {
  CheckCircle,
  MoreHorizontal,
  Trash2,
  Pencil,
  MapPin,
  Download,
  Search,
  SlidersHorizontal,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  X,
  Mail
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { deleteProviderAction } from "@/lib/actions/provider.actions";
import type { ProviderWithEquipment, EquipmentType } from "@/lib/schemas";
import { formatDistanceToNowSafely } from "@/lib/utils/date";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { createLogger } from "@/lib/utils/logger/logger";
import { getEquipmentColor } from "@/lib/utils/equipment-utils";
import { DataTableFacetedFilter } from "@/components/ui/tables/faceted-filter";

const logger = createLogger("ProvidersDataTable");

interface ProvidersDataTableProps {
  data: ProviderWithEquipment[];
  countries?: any[]; // Optional, kept for backward compatibility
}

// Export function to convert data to CSV
const exportToCSV = (data: any[], filename: string) => {
  if (data.length === 0) {
    toast.error("No data to export");
    return;
  }

  try {
    // Get headers from the first item
    const headers = Object.keys(data[0]).filter(
      (key) => key !== "equipments" && key !== "structured_address"
    );

    // Create CSV content
    const csvContent = [
      headers.join(","), // Header row
      ...data.map((item) =>
        headers
          .map((key) => {
            const value = item[key];
            // Handle different value types
            if (value === null || value === undefined) return "";
            if (typeof value === "boolean") return value ? "Yes" : "No";
            if (typeof value === "object") return JSON.stringify(value);
            return `"${String(value).replace(/"/g, '""')}"`;
          })
          .join(",")
      ),
    ].join("\n");

    // Create a blob and download link
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);

    // Use a safer approach to trigger download
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", `${filename}.csv`);
    link.style.display = "none";

    // Add to DOM, click, and clean up
    document.body.appendChild(link);
    link.click();

    // Clean up
    setTimeout(() => {
      document.body.removeChild(link);
      URL.revokeObjectURL(url); // Free up memory by revoking the object URL
    }, 100);

    toast.success(`Exported ${data.length} providers to CSV`);
  } catch (error) {
    logger.error("Error exporting to CSV:", error);
    toast.error("Failed to export data to CSV");
  }
};

export function ProvidersDataTable({
  data,
}: ProvidersDataTableProps) {
  const router = useRouter();

  // State for table features
  const [sorting, setSorting] = useState<SortingState>([
    {
      id: "updated_at",
      desc: true,
    },
  ]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState("");
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({
    tax_id: false,
    equipment_category: false,
  });


  // Custom filter function for equipment categories
  const filterEquipmentByCategory = useCallback(
    (row: any, _id: string, filterValue: string | string[]) => {
      const provider = row.original;
      if (!provider.equipments || provider.equipments.length === 0) {
        return false;
      }

      // Handle both single string and array of strings
      const filterValues = Array.isArray(filterValue) ? filterValue : [filterValue];

      // If no filter values, show all
      if (filterValues.length === 0) {
        return true;
      }

      return provider.equipments.some(
        (equipment: EquipmentType) =>
          equipment.category &&
          filterValues.some(value =>
            equipment.category.toLowerCase() === value.toLowerCase()
          )
      );
    },
    []
  );

  // Define columns with enhanced features
  const columns = useMemo<ColumnDef<ProviderWithEquipment>[]>(
    () => [
      {
        id: "select",
        header: ({ table }) => (
          <Checkbox
            checked={
              table.getIsAllPageRowsSelected() ||
              (table.getIsSomePageRowsSelected() && "indeterminate")
            }
            onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
            aria-label="Select all"
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
          />
        ),
        enableSorting: false,
        enableHiding: false,
        size: 40,
      },
      {
        accessorKey: "name",
        header: ({ column }) => (
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
              className="p-0 hover:bg-transparent"
            >
              <span>Name</span>
              {column.getIsSorted() === "asc" ? (
                <ArrowUp className="ml-1 h-4 w-4" />
              ) : column.getIsSorted() === "desc" ? (
                <ArrowDown className="ml-1 h-4 w-4" />
              ) : (
                <ArrowUpDown className="ml-1 h-4 w-4" />
              )}
            </Button>
          </div>
        ),
        cell: ({ row }) => (
          <div className="font-medium">
            <Link
              href={`/dashboard/providers/${row.original.id}`}
              className="hover:underline"
            >
              {row.getValue("name")}
            </Link>
          </div>
        ),
        filterFn: "includesString",
        size: 200,
      },
      {
        accessorKey: "tax_id",
        header: ({ column }) => (
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
              className="p-0 hover:bg-transparent"
            >
              <span>Tax ID</span>
              {column.getIsSorted() === "asc" ? (
                <ArrowUp className="ml-1 h-4 w-4" />
              ) : column.getIsSorted() === "desc" ? (
                <ArrowDown className="ml-1 h-4 w-4" />
              ) : (
                <ArrowUpDown className="ml-1 h-4 w-4" />
              )}
            </Button>
          </div>
        ),
        meta: {
          displayName: "Tax ID",
        },
        size: 150,
      },
      {
        accessorKey: "status",
        header: ({ column }) => (
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
              className="p-0 hover:bg-transparent"
            >
              <span>Status</span>
              {column.getIsSorted() === "asc" ? (
                <ArrowUp className="ml-1 h-4 w-4" />
              ) : column.getIsSorted() === "desc" ? (
                <ArrowDown className="ml-1 h-4 w-4" />
              ) : (
                <ArrowUpDown className="ml-1 h-4 w-4" />
              )}
            </Button>
          </div>
        ),
        cell: ({ row }) => {
          const status = row.getValue("status") as string;
          return (
            <div className="flex items-center">
              <div
                className={`w-2 h-2 rounded-full mr-2 ${
                  status === "active"
                    ? "bg-green-500"
                    : status === "pending"
                      ? "bg-yellow-500"
                      : status === "suspended"
                        ? "bg-red-500"
                        : "bg-muted-foreground"
                }`}
              />
              <Badge
                variant={
                  status === "active"
                    ? "default"
                    : status === "pending"
                      ? "secondary"
                      : status === "suspended"
                        ? "destructive"
                        : "outline"
                }
                className="capitalize"
              >
                {status}
              </Badge>
            </div>
          );
        },
        filterFn: (row, id, value) => {
          return value.includes(row.getValue(id));
        },
        size: 120,
      },
      {
        accessorKey: "verified",
        header: ({ column }) => (
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
              className="p-0 hover:bg-transparent"
            >
              <span>Verified</span>
              {column.getIsSorted() === "asc" ? (
                <ArrowUp className="ml-1 h-4 w-4" />
              ) : column.getIsSorted() === "desc" ? (
                <ArrowDown className="ml-1 h-4 w-4" />
              ) : (
                <ArrowUpDown className="ml-1 h-4 w-4" />
              )}
            </Button>
          </div>
        ),
        cell: ({ row }) => {
          const verified = row.getValue("verified") as boolean;
          return verified ? (
            <Badge
              variant="outline"
              className="bg-accent text-accent-foreground border-accent-foreground/20 flex items-center gap-1"
            >
              <CheckCircle className="h-3 w-3" />
              Verified
            </Badge>
          ) : (
            <Badge
              variant="outline"
              className="bg-muted text-muted-foreground border-muted-foreground/20"
            >
              Not Verified
            </Badge>
          );
        },
        filterFn: (row, id, value) => {
          return value.includes(row.getValue(id));
        },
        size: 120,
      },
      {
        id: "equipment_category",
        header: () => (
          <div className="flex items-center space-x-1">
            <span>Equipment Category</span>
          </div>
        ),
        filterFn: filterEquipmentByCategory,
        enableHiding: false,
        size: 0,
      },
      {
        id: "equipments",
        header: () => (
          <div className="flex items-center space-x-1">
            <span>Equipment</span>
          </div>
        ),
        cell: ({ row }) => {
          const provider = row.original;
          const equipments = provider.equipments || [];

          if (equipments.length === 0) {
            return (
              <span className="text-muted-foreground text-sm">No equipment</span>
            );
          }

          // Show up to 3 equipment items directly, with a +X more if there are more
          const displayEquipments = equipments.slice(0, 3);
          const remainingCount = equipments.length - displayEquipments.length;

          return (
            <div className="flex flex-wrap gap-1 max-w-[250px]">
              <TooltipProvider>
                {displayEquipments.map((equipment: EquipmentType) => {
                  const colors = getEquipmentColor(equipment.category);
                  return (
                    <Tooltip key={equipment.id}>
                      <TooltipTrigger asChild>
                        <Badge
                          variant="outline"
                          className={`text-xs px-2 py-0.5 h-6 ${colors.bg} ${colors.text} ${colors.border} flex items-center gap-1 whitespace-nowrap`}
                        >
                          <span className="truncate max-w-[150px]">
                            {equipment.name}
                          </span>
                        </Badge>
                      </TooltipTrigger>
                      <TooltipContent side="bottom" className="p-3 max-w-[250px]">
                        <p className="font-medium text-sm">{equipment.name}</p>
                        {equipment.description && (
                          <p className="text-xs mt-1.5">{equipment.description}</p>
                        )}
                      </TooltipContent>
                    </Tooltip>
                  );
                })}

                {remainingCount > 0 && (
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Badge
                        variant="outline"
                        className="text-xs px-2 py-0.5 h-6 bg-muted text-muted-foreground border-muted-foreground/20"
                      >
                        +{remainingCount} more
                      </Badge>
                    </TooltipTrigger>
                    <TooltipContent side="bottom" className="p-3">
                      <div className="max-w-[250px]">
                        <p className="font-medium text-sm mb-2">
                          Additional Equipment:
                        </p>
                        <div className="space-y-1.5">
                          {equipments.slice(3).map((equipment: EquipmentType) => (
                            <div
                              key={equipment.id}
                              className="flex flex-col gap-0.5"
                            >
                              <span className="text-sm font-medium">
                                {equipment.name}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </TooltipContent>
                  </Tooltip>
                )}
              </TooltipProvider>
            </div>
          );
        },
        size: 250,
      },
      {
        accessorKey: "rfqInvitationCount",
        header: ({ column }) => (
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
              className="p-0 hover:bg-transparent"
            >
              <span>RFQ Invitations</span>
              {column.getIsSorted() === "asc" ? (
                <ArrowUp className="ml-1 h-4 w-4" />
              ) : column.getIsSorted() === "desc" ? (
                <ArrowDown className="ml-1 h-4 w-4" />
              ) : (
                <ArrowUpDown className="ml-1 h-4 w-4" />
              )}
            </Button>
          </div>
        ),
        cell: ({ row }) => {
          const count = row.getValue("rfqInvitationCount") as number;

          return (
            <div className="flex items-center">
              <Mail className="h-4 w-4 mr-2 text-muted-foreground" />
              <span className="font-medium">{count || 0}</span>
            </div>
          );
        },
        size: 150,
      },
      {
        accessorKey: "updated_at",
        header: ({ column }) => (
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
              className="p-0 hover:bg-transparent"
            >
              <span>Last Updated</span>
              {column.getIsSorted() === "asc" ? (
                <ArrowUp className="ml-1 h-4 w-4" />
              ) : column.getIsSorted() === "desc" ? (
                <ArrowDown className="ml-1 h-4 w-4" />
              ) : (
                <ArrowUpDown className="ml-1 h-4 w-4" />
              )}
            </Button>
          </div>
        ),
        cell: ({ row }) => {
          const updatedAt = row.getValue("updated_at") as string;
          return (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="text-muted-foreground">
                    {formatDistanceToNowSafely(updatedAt)}
                  </div>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  {new Date(updatedAt).toLocaleString()}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          );
        },
        size: 150,
      },
      {
        id: "actions",
        cell: function Cell({ row }) {
          const provider = row.original;
          const [isDeleting, setIsDeleting] = useState(false);

          const handleDelete = async () => {
            try {
              setIsDeleting(true);
              const result = await deleteProviderAction(provider.id);
              if (!result.success) {
                toast.error(result.error || "Failed to delete provider");
                return;
              }
              toast.success("Provider deleted successfully");
              router.refresh();
            } catch (error) {
              logger.error(error instanceof Error ? error.message : String(error));
              toast.error("Failed to delete provider");
            } finally {
              setIsDeleting(false);
            }
          };

          return (
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="sm" asChild className="h-8 px-2">
                <Link href={`/dashboard/providers/${provider.id}`}>
                  <Pencil className="h-4 w-4 mr-1" />
                  Edit
                </Link>
              </Button>
              <Button variant="ghost" size="sm" asChild className="h-8 px-2">
                <Link href={`/dashboard/providers/${provider.id}?tab=routes`}>
                  <MapPin className="h-4 w-4 mr-1" />
                  Routes
                </Link>
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8 p-0">
                    <span className="sr-only">Open menu</span>
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                  <DropdownMenuItem
                    onClick={() => navigator.clipboard.writeText(provider.id)}
                  >
                    Copy ID
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <DropdownMenuItem
                        className="flex items-center text-red-600 hover:!text-red-700"
                        onSelect={(e: Event) => e.preventDefault()}
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete Provider
                      </DropdownMenuItem>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>
                          Are you absolutely sure?
                        </AlertDialogTitle>
                        <AlertDialogDescription>
                          This action cannot be undone. This will permanently delete
                          the provider and all associated data.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={handleDelete}
                          className="bg-red-600 hover:bg-red-700"
                          disabled={isDeleting}
                        >
                          {isDeleting ? "Deleting..." : "Delete"}
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          );
        },
        size: 200,
      },
    ],
    [router, filterEquipmentByCategory]
  );

  // Create table instance with all features
  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnFilters,
      globalFilter,
      rowSelection,
      columnVisibility,
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    globalFilterFn: (row, columnId, filterValue) => {
      const safeValue = String(row.getValue(columnId) || "").toLowerCase();
      return safeValue.includes(String(filterValue).toLowerCase());
    },
  });

  // Status options for filtering
  const statusOptions = [
    { label: "Active", value: "active" },
    { label: "Inactive", value: "inactive" },
    { label: "Pending", value: "pending" },
    { label: "Suspended", value: "suspended" },
  ];

  // Verified options for filtering
  const verifiedOptions = [
    { label: "Verified", value: "true" },
    { label: "Not Verified", value: "false" },
  ];

  // Extract unique equipment categories from data
  const equipmentCategories = useMemo(() => {
    // Define a set of standard equipment categories to ensure they're always available
    const standardCategories = new Set([
      'trailer',
      'container',
      'vehicle',
      'chassis',
      'other'
    ]);

    // Add categories from the data
    data.forEach(provider => {
      if (provider.equipments && provider.equipments.length > 0) {
        provider.equipments.forEach(equipment => {
          if (equipment.category) {
            standardCategories.add(equipment.category.toLowerCase());
          }
        });
      }
    });

    // Convert to array and format for the filter
    return Array.from(standardCategories)
      .sort() // Sort alphabetically
      .map(category => ({
        label: category.charAt(0).toUpperCase() + category.slice(1),
        value: category,
      }));
  }, [data]);

  // Handle export
  const handleExport = useCallback(() => {
    const selectedRows = Object.keys(rowSelection).map(
      (index) => data[parseInt(index)]
    );

    if (selectedRows.length > 0) {
      exportToCSV(selectedRows, "selected-providers");
    } else {
      exportToCSV(data, "all-providers");
    }
  }, [data, rowSelection]);

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="relative w-64">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground pointer-events-none" />
            <Input
              placeholder="Search providers..."
              value={globalFilter ?? ""}
              onChange={(e) => setGlobalFilter(e.target.value)}
              className="pl-8 h-9 focus-visible:ring-1 focus-visible:ring-offset-0 border-muted"
              type="search"
              autoComplete="off"
            />
          </div>
          <DataTableFacetedFilter
            column={table.getColumn("status")}
            title="Status"
            options={statusOptions}
          />
          <DataTableFacetedFilter
            column={table.getColumn("verified")}
            title="Verified"
            options={verifiedOptions}
          />
          {equipmentCategories.length > 0 && (
            <DataTableFacetedFilter
              column={table.getColumn("equipment_category")}
              title="Equipment"
              options={equipmentCategories}
            />
          )}
          {(table.getState().columnFilters.length > 0 || globalFilter) && (
            <Button
              variant="ghost"
              onClick={() => {
                table.resetColumnFilters();
                setGlobalFilter("");
              }}
              className="h-8 px-2 lg:px-3"
            >
              Reset
              <X className="ml-2 h-4 w-4" />
            </Button>
          )}
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleExport}
            disabled={data.length === 0}
          >
            <Download className="mr-2 h-4 w-4" />
            {Object.keys(rowSelection).length > 0
              ? "Export Selected"
              : "Export All"}
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="ml-auto">
                <SlidersHorizontal className="mr-2 h-4 w-4" />
                View
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[150px]">
              <DropdownMenuLabel>Toggle columns</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {table
                .getAllColumns()
                .filter(
                  (column) =>
                    typeof column.accessorFn !== "undefined" && column.getCanHide()
                )
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) => column.toggleVisibility(!!value)}
                    >
                      {column.columnDef.meta?.displayName ?? column.id}
                    </DropdownMenuCheckboxItem>
                  );
                })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <div className="rounded-md border">
        <Table className="w-full">
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead
                    key={header.id}
                    style={{
                      width: header.getSize(),
                    }}
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell
                      key={cell.id}
                      style={{
                        width: cell.column.getSize(),
                      }}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredSelectedRowModel().rows.length} of{" "}
          {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
        <div className="flex items-center space-x-6 lg:space-x-8">
          <div className="flex items-center space-x-2">
            <p className="text-sm font-medium">Rows per page</p>
            <select
              value={table.getState().pagination.pageSize}
              onChange={(e) => {
                table.setPageSize(Number(e.target.value));
              }}
              className="h-8 w-[70px] rounded-md border border-input bg-background px-2 py-1 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            >
              {[10, 20, 30, 40, 50].map((pageSize) => (
                <option key={pageSize} value={pageSize}>
                  {pageSize}
                </option>
              ))}
            </select>
          </div>
          <div className="flex w-[100px] items-center justify-center text-sm font-medium">
            Page {table.getState().pagination.pageIndex + 1} of{" "}
            {table.getPageCount()}
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={() => table.setPageIndex(0)}
              disabled={!table.getCanPreviousPage()}
            >
              <span className="sr-only">Go to first page</span>
              <ChevronsLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              <span className="sr-only">Go to previous page</span>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              <span className="sr-only">Go to next page</span>
              <ChevronRight className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={() => table.setPageIndex(table.getPageCount() - 1)}
              disabled={!table.getCanNextPage()}
            >
              <span className="sr-only">Go to last page</span>
              <ChevronsRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
