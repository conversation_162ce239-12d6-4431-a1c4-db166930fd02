"use client";
import { useState } from "react";
import { toast } from "@/components/ui/sonner";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Star, Plus } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { ContactFormValues, ContactFormSchema } from "@/lib/schemas";
import {
  createProviderContactAction,
  updateProviderContactAction,
  deleteProviderContactAction,
} from "@/lib/actions/provider-contact.actions";
import { type ProviderContact } from "@/lib/schemas";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal } from "lucide-react";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";
import { useRouter } from "next/navigation";
import { createLogger } from "@/lib/utils/logger/logger";

const logger = createLogger("ContactsPanel");

// ProviderContact type is imported from @/lib/schemas

interface ContactFormProps {
  providerId: string;
  initialData?: ProviderContact;
  onSuccess?: () => void;
}

function ContactForm({ providerId, initialData, onSuccess }: ContactFormProps) {
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const form = useForm<ContactFormValues>({
    resolver: zodResolver(ContactFormSchema),
    defaultValues: {
      name: initialData?.name || "",
      email: initialData?.email || "",
      phone: initialData?.phone || "",
      role: initialData?.role || "",
      is_primary: initialData?.is_primary || false,
    },
  });

  async function onSubmit(data: ContactFormValues) {
    try {
      setLoading(true);
      logger.info("Submitting contact form with data:", data);

      if (initialData) {
        logger.info("Updating existing contact:", initialData.id);
        const result = await updateProviderContactAction(
          initialData.id,
          providerId,
          {
            ...data,
          },
        );
        logger.info("Update result:", result);

        if (result.success) {
          toast.success("Contact updated successfully");
        } else {
          logger.error("Server returned error:", result.error);
          toast.error(result.error || "Failed to update contact");
          return;
        }
      } else {
        logger.info("Creating new contact for provider:", providerId);
        const result = await createProviderContactAction({
          ...data,
          provider_id: providerId,
        });
        logger.info("Creation result:", result);

        if (result.success) {
          toast.success("Contact created successfully");
        } else {
          logger.error("Server returned error:", result.error);
          toast.error(result.error || "Failed to create contact");
          return;
        }
      }

      // Force a refresh to update the UI with the new/updated contact
      logger.info("Refreshing UI...");
      router.refresh();
      onSuccess?.();
    } catch (error) {
      logger.error("Exception during contact submission:", error);
      toast.error("Something went wrong");
    } finally {
      setLoading(false);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input {...field} value={field.value || ''} placeholder="Enter name (optional)" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center">
                Email <span className="text-red-500 ml-1">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  type="email"
                  {...field}
                  placeholder="Enter email (required)"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="phone"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Phone</FormLabel>
              <FormControl>
                <Input {...field} value={field.value || ''} placeholder="Enter phone" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="role"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Role</FormLabel>
              <FormControl>
                <Input {...field} value={field.value || ''} placeholder="Enter role" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="is_primary"
          render={({ field }) => (
            <FormItem className="flex flex-row items-start space-x-3 space-y-0">
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
              <div className="space-y-1 leading-none">
                <FormLabel>Primary Contact</FormLabel>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex justify-end">
          <Button type="submit" disabled={loading}>
            {initialData ? "Update" : "Create"} Contact
          </Button>
        </div>
      </form>
    </Form>
  );
}

interface ContactFormModalProps {
  providerId: string;
  onSuccess?: () => void;
}

function ContactFormModal({ providerId, onSuccess }: ContactFormModalProps) {
  const [open, setOpen] = useState(false);
  const router = useRouter();

  const handleSuccess = () => {
    setOpen(false);
    // Force a refresh to update the contacts list
    router.refresh();
    onSuccess?.();
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Add Contact
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add Contact</DialogTitle>
        </DialogHeader>
        <ContactForm providerId={providerId} onSuccess={handleSuccess} />
      </DialogContent>
    </Dialog>
  );
}

interface ContactsPanelProps {
  providerId: string;
  contacts: ProviderContact[];
}

export default function ContactsPanel({
  providerId,
  contacts,
}: ContactsPanelProps) {
  const router = useRouter();
  const [selectedContact, setSelectedContact] = useState<
    ProviderContact | undefined
  >(undefined);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [deletingContactId, setDeletingContactId] = useState<string | null>(
    null,
  );
  const [deleteAlertOpen, setDeleteAlertOpen] = useState(false);
  const [contactToDelete, setContactToDelete] =
    useState<ProviderContact | null>(null);

  const handleDelete = async () => {
    if (!contactToDelete) return;

    setDeletingContactId(contactToDelete.id);
    try {
      const result = await deleteProviderContactAction(
        contactToDelete.id,
        contactToDelete.provider_id,
      );

      if (!result.success) {
        toast.error(result.error || "Failed to delete contact");
        return;
      }

      toast.success("Contact deleted successfully");
      // Force a refresh to update the UI after deletion
      router.refresh();
      setContactToDelete(null);
      setDeleteAlertOpen(false);
    } catch (error) {
      logger.error("Error deleting contact:", error);
      toast.error("Failed to delete contact");
    } finally {
      setDeletingContactId(null);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold">Contacts</h2>
        <ContactFormModal providerId={providerId} />
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Phone</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Primary</TableHead>
              <TableHead className="w-[100px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {contacts.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="h-24 text-center">
                  No contacts found.
                </TableCell>
              </TableRow>
            ) : (
              contacts.map((contact) => (
                <TableRow key={contact.id}>
                  <TableCell>{contact.name || "-"}</TableCell>
                  <TableCell>{contact.email}</TableCell>
                  <TableCell>{contact.phone || "-"}</TableCell>
                  <TableCell>{contact.role || "-"}</TableCell>
                  <TableCell>
                    {contact.is_primary ? (
                      <Badge
                        variant="outline"
                        className="bg-accent text-accent-foreground border-accent-foreground/20 flex items-center gap-1"
                      >
                        <Star className="h-3 w-3" />
                        Primary
                      </Badge>
                    ) : (
                      <Badge
                        variant="outline"
                        className="bg-muted text-muted-foreground border-muted-foreground/20"
                      >
                        Secondary
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 p-0"
                          >
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onSelect={() => {
                              setSelectedContact(contact);
                              setEditModalOpen(true);
                            }}
                          >
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onSelect={() => {
                              setContactToDelete(contact);
                              setDeleteAlertOpen(true);
                            }}
                            className="text-red-600"
                          >
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Edit Contact Dialog - Moved outside map */}
      <Dialog
        open={editModalOpen}
        onOpenChange={(isOpen) => {
          setEditModalOpen(isOpen);
          if (!isOpen) {
            setSelectedContact(undefined);
          }
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Contact</DialogTitle>
          </DialogHeader>
          {selectedContact && (
            <ContactForm
              providerId={providerId}
              initialData={selectedContact}
              onSuccess={() => {
                // Force a refresh to update the UI after edit
                router.refresh();
                setEditModalOpen(false);
              }}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Contact Alert Dialog */}
      <AlertDialog open={deleteAlertOpen} onOpenChange={setDeleteAlertOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete this contact.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={!!deletingContactId}
            >
              {deletingContactId ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
