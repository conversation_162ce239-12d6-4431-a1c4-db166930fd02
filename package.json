{"name": "steelflow", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "playwright test", "test:unit": "SKIP_WEB_SERVER=true playwright test tests/unit", "test:headed": "playwright test --headed", "test:ui": "playwright test --ui"}, "dependencies": {"@google/genai": "^0.12.0", "@googlemaps/addressvalidation": "^3.0.1", "@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-aspect-ratio": "^1.1.6", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-context-menu": "^2.2.14", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-hover-card": "^1.1.13", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-menubar": "^1.1.14", "@radix-ui/react-navigation-menu": "^1.2.12", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toggle": "^1.1.8", "@radix-ui/react-toggle-group": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.6", "@sendgrid/mail": "^8.1.5", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.76.2", "@tanstack/react-query-devtools": "^5.76.2", "@tanstack/react-table": "^8.21.3", "@unkey/api": "^0.36.0", "@unkey/nextjs": "^0.18.9", "@vis.gl/react-google-maps": "^1.5.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "google-auth-library": "^9.15.1", "googleapis": "^148.0.0", "input-otp": "^1.4.2", "lucide-react": "^0.483.0", "marked": "^15.0.11", "next": "^15.3.2", "next-themes": "^0.4.6", "nextjs-toploader": "^3.8.16", "postal-code-validator": "^1.0.3", "react": "19.0.0-rc.1", "react-country-flag": "^3.1.0", "react-dom": "19.0.0-rc.1", "react-hook-form": "^7.56.3", "react-resizable-panels": "^2.1.9", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "use-places-autocomplete": "^4.0.1", "vaul": "^1.1.2", "zod": "^3.24.4"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.26.0", "@playwright/test": "^1.52.0", "@tailwindcss/postcss": "^4.1.6", "@types/google.maps": "^3.58.1", "@types/marked": "^5.0.2", "@types/node": "^20.17.46", "@types/react": "^19.1.3", "@types/react-dom": "^19.1.3", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "eslint": "^9.26.0", "eslint-config-next": "15.2.3", "null-loader": "^4.0.1", "supabase-cli": "^0.0.21", "supazod": "^1.2.3", "tailwindcss": "^4.1.6", "ts-node": "^10.9.2", "tsx": "^4.19.4", "tw-animate-css": "^1.3.0", "typescript": "^5.8.3"}}